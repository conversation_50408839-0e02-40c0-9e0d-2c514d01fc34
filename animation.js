// Water Infrastructure 3D Animation
class WaterInfrastructureAnimation {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.animationId = null;
        this.isPlaying = false;
        this.startTime = 0;
        this.duration = 8000; // 8 seconds in milliseconds
        
        // Animation objects
        this.stp = null;
        this.pumpHouse = null;
        this.pipeline = null;
        this.field = null;
        this.waterFlow = null;
        
        // Labels
        this.labels = {
            stp: document.getElementById('stp-label'),
            pump: document.getElementById('pump-label'),
            pipeline: document.getElementById('pipeline-label')
        };
        
        // Controls
        this.playBtn = document.getElementById('play-btn');
        this.pauseBtn = document.getElementById('pause-btn');
        this.resetBtn = document.getElementById('reset-btn');
        this.progressFill = document.getElementById('progress-fill');
        
        this.init();
        this.setupControls();
    }
    
    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Set initial camera position (far left)
        this.camera.position.set(-50, 15, 30);
        this.camera.lookAt(-40, 0, 0);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Add renderer to DOM
        document.getElementById('canvas-container').appendChild(this.renderer.domElement);
        
        // Add lighting
        this.setupLighting();
        
        // Create 3D objects
        this.createSTP();
        this.createPumpHouse();
        this.createPipeline();
        this.createField();
        this.createGround();
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Initial render
        this.renderer.render(this.scene, this.camera);
    }
    
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // Additional fill light
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-50, 30, 50);
        this.scene.add(fillLight);
    }
    
    createGround() {
        // Ground plane
        const groundGeometry = new THREE.PlaneGeometry(200, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        // Road/path
        const roadGeometry = new THREE.PlaneGeometry(200, 8);
        const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.rotation.x = -Math.PI / 2;
        road.position.y = -1.9;
        road.position.z = 5;
        this.scene.add(road);
    }
    
    createSTP() {
        this.stp = new THREE.Group();
        
        // Main STP building
        const buildingGeometry = new THREE.BoxGeometry(12, 8, 10);
        const buildingMaterial = new THREE.MeshLambertMaterial({ color: 0x4CAF50 });
        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        building.position.set(-45, 4, 0);
        building.castShadow = true;
        this.stp.add(building);
        
        // STP tanks
        const tankGeometry = new THREE.CylinderGeometry(3, 3, 2, 16);
        const tankMaterial = new THREE.MeshLambertMaterial({ color: 0x2196F3 });
        
        for (let i = 0; i < 3; i++) {
            const tank = new THREE.Mesh(tankGeometry, tankMaterial);
            tank.position.set(-45 + i * 4, 1, -8);
            tank.castShadow = true;
            this.stp.add(tank);
        }
        
        // Outlet pipe
        const pipeGeometry = new THREE.CylinderGeometry(0.5, 0.5, 8, 16);
        const pipeMaterial = new THREE.MeshLambertMaterial({ color: 0x607D8B });
        const outletPipe = new THREE.Mesh(pipeGeometry, pipeMaterial);
        outletPipe.rotation.z = Math.PI / 2;
        outletPipe.position.set(-35, 2, 0);
        this.stp.add(outletPipe);
        
        // Collection sump
        const sumpGeometry = new THREE.BoxGeometry(6, 3, 6);
        const sumpMaterial = new THREE.MeshLambertMaterial({ color: 0x03A9F4 });
        const sump = new THREE.Mesh(sumpGeometry, sumpMaterial);
        sump.position.set(-28, 0.5, 0);
        this.stp.add(sump);
        
        this.scene.add(this.stp);
    }
    
    createPumpHouse() {
        this.pumpHouse = new THREE.Group();
        
        // Pump house building
        const houseGeometry = new THREE.BoxGeometry(8, 6, 8);
        const houseMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xFFC107,
            transparent: true,
            opacity: 0.7
        });
        const house = new THREE.Mesh(houseGeometry, houseMaterial);
        house.position.set(-18, 3, 0);
        house.castShadow = true;
        this.pumpHouse.add(house);
        
        // Internal pump (visible through transparent walls)
        const pumpGeometry = new THREE.CylinderGeometry(1.5, 1.5, 4, 16);
        const pumpMaterial = new THREE.MeshLambertMaterial({ color: 0xFF5722 });
        const pump = new THREE.Mesh(pumpGeometry, pumpMaterial);
        pump.position.set(-18, 2, 0);
        this.pumpHouse.add(pump);
        
        // Pump motor
        const motorGeometry = new THREE.BoxGeometry(2, 1.5, 2);
        const motorMaterial = new THREE.MeshLambertMaterial({ color: 0x795548 });
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(-18, 4.5, 0);
        this.pumpHouse.add(motor);
        
        this.scene.add(this.pumpHouse);
    }

    createPipeline() {
        this.pipeline = new THREE.Group();

        // Underground pipeline segments
        const pipeGeometry = new THREE.CylinderGeometry(0.8, 0.8, 10, 16);
        const pipeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });

        // Create pipeline segments from pump house to field
        for (let i = 0; i < 8; i++) {
            const pipeSegment = new THREE.Mesh(pipeGeometry, pipeMaterial);
            pipeSegment.rotation.z = Math.PI / 2;
            pipeSegment.position.set(-10 + i * 10, -3, 0);
            this.pipeline.add(pipeSegment);
        }

        // Pipeline crossing under road (deeper section)
        const crossingPipe = new THREE.Mesh(pipeGeometry, pipeMaterial);
        crossingPipe.rotation.z = Math.PI / 2;
        crossingPipe.position.set(20, -5, 5);
        this.pipeline.add(crossingPipe);

        // Vertical outlet at field
        const outletGeometry = new THREE.CylinderGeometry(0.8, 0.8, 6, 16);
        const outlet = new THREE.Mesh(outletGeometry, pipeMaterial);
        outlet.position.set(60, -1, 0);
        this.pipeline.add(outlet);

        this.scene.add(this.pipeline);
    }

    createField() {
        this.field = new THREE.Group();

        // Agricultural field base
        const fieldGeometry = new THREE.PlaneGeometry(30, 25);
        const fieldMaterial = new THREE.MeshLambertMaterial({ color: 0x4CAF50 });
        const fieldBase = new THREE.Mesh(fieldGeometry, fieldMaterial);
        fieldBase.rotation.x = -Math.PI / 2;
        fieldBase.position.set(70, -1.8, 0);
        this.field.add(fieldBase);

        // Crops (simple green boxes)
        for (let x = 0; x < 6; x++) {
            for (let z = 0; z < 5; z++) {
                const cropGeometry = new THREE.BoxGeometry(1, 2, 1);
                const cropMaterial = new THREE.MeshLambertMaterial({ color: 0x8BC34A });
                const crop = new THREE.Mesh(cropGeometry, cropMaterial);
                crop.position.set(60 + x * 4, 1, -10 + z * 4);
                crop.castShadow = true;
                this.field.add(crop);
            }
        }

        // Irrigation system
        this.createIrrigationSystem();

        this.scene.add(this.field);
    }

    createIrrigationSystem() {
        // Drip irrigation lines
        const lineGeometry = new THREE.CylinderGeometry(0.1, 0.1, 25, 8);
        const lineMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });

        for (let i = 0; i < 6; i++) {
            const line = new THREE.Mesh(lineGeometry, lineMaterial);
            line.rotation.x = Math.PI / 2;
            line.position.set(60 + i * 4, 0.2, 0);
            this.field.add(line);
        }

        // Sprinkler heads
        for (let x = 0; x < 6; x++) {
            for (let z = 0; z < 5; z++) {
                const sprinklerGeometry = new THREE.SphereGeometry(0.2, 8, 8);
                const sprinklerMaterial = new THREE.MeshLambertMaterial({ color: 0x2196F3 });
                const sprinkler = new THREE.Mesh(sprinklerGeometry, sprinklerMaterial);
                sprinkler.position.set(60 + x * 4, 0.5, -10 + z * 4);
                this.field.add(sprinkler);
            }
        }
    }

    setupControls() {
        this.playBtn.addEventListener('click', () => this.startAnimation());
        this.pauseBtn.addEventListener('click', () => this.pauseAnimation());
        this.resetBtn.addEventListener('click', () => this.resetAnimation());
    }

    startAnimation() {
        if (!this.isPlaying) {
            this.isPlaying = true;
            this.startTime = Date.now();
            this.playBtn.disabled = true;
            this.pauseBtn.disabled = false;
            this.animate();
        }
    }

    pauseAnimation() {
        this.isPlaying = false;
        this.playBtn.disabled = false;
        this.pauseBtn.disabled = true;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    resetAnimation() {
        this.pauseAnimation();
        this.startTime = 0;
        this.progressFill.style.width = '0%';

        // Reset camera position
        this.camera.position.set(-50, 15, 30);
        this.camera.lookAt(-40, 0, 0);

        // Hide all labels
        Object.values(this.labels).forEach(label => {
            label.style.opacity = '0';
        });

        this.renderer.render(this.scene, this.camera);
    }

    animate() {
        if (!this.isPlaying) return;

        const currentTime = Date.now();
        const elapsed = currentTime - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);

        // Update progress bar
        this.progressFill.style.width = (progress * 100) + '%';

        // Camera animation (rightward pan)
        this.updateCamera(progress);

        // Label timing
        this.updateLabels(progress);

        // Water flow animation
        this.updateWaterFlow(progress);

        // Render the scene
        this.renderer.render(this.scene, this.camera);

        // Continue animation or stop at end
        if (progress < 1) {
            this.animationId = requestAnimationFrame(() => this.animate());
        } else {
            this.pauseAnimation();
        }
    }

    updateCamera(progress) {
        // Smooth rightward pan from left (-50) to right (80) over 8 seconds
        const startX = -50;
        const endX = 80;
        const currentX = startX + (endX - startX) * progress;

        // Smooth camera movement with easing
        const easedProgress = this.easeInOutCubic(progress);
        const smoothX = startX + (endX - startX) * easedProgress;

        this.camera.position.x = smoothX;
        this.camera.position.y = 15 + Math.sin(progress * Math.PI) * 2; // Slight vertical movement
        this.camera.position.z = 30;

        // Look ahead of camera position
        this.camera.lookAt(smoothX + 10, 0, 0);
    }

    updateLabels(progress) {
        const timeInSeconds = progress * 8;

        // STP label (0-2 seconds)
        if (timeInSeconds >= 0 && timeInSeconds <= 2.5) {
            this.labels.stp.style.opacity = '1';
        } else {
            this.labels.stp.style.opacity = '0';
        }

        // Pump house label (2-4 seconds)
        if (timeInSeconds >= 1.8 && timeInSeconds <= 4.5) {
            this.labels.pump.style.opacity = '1';
        } else {
            this.labels.pump.style.opacity = '0';
        }

        // Pipeline label (4-7 seconds)
        if (timeInSeconds >= 3.8 && timeInSeconds <= 7.5) {
            this.labels.pipeline.style.opacity = '1';
        } else {
            this.labels.pipeline.style.opacity = '0';
        }
    }

    updateWaterFlow(progress) {
        const timeInSeconds = progress * 8;

        // Animate water flow through system
        if (timeInSeconds >= 1) {
            this.animateWaterInSTP();
        }

        if (timeInSeconds >= 2.5) {
            this.animatePumpOperation();
        }

        if (timeInSeconds >= 4) {
            this.animateWaterInPipeline();
        }

        if (timeInSeconds >= 7) {
            this.animateIrrigation();
        }
    }

    animateWaterInSTP() {
        // Add water flow particles or color changes to show treated water
        // This could be enhanced with particle systems
    }

    animatePumpOperation() {
        // Rotate pump to show operation
        if (this.pumpHouse && this.pumpHouse.children[1]) {
            this.pumpHouse.children[1].rotation.y += 0.1;
        }
    }

    animateWaterInPipeline() {
        // Could add flowing water animation through pipeline
        // For now, we'll use color changes to indicate flow
    }

    animateIrrigation() {
        // Animate sprinkler operation
        if (this.field) {
            const sprinklers = this.field.children.filter(child =>
                child.geometry instanceof THREE.SphereGeometry
            );
            sprinklers.forEach((sprinkler, index) => {
                sprinkler.scale.setScalar(1 + 0.2 * Math.sin(Date.now() * 0.01 + index));
            });
        }
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// Initialize the animation when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new WaterInfrastructureAnimation();
});
