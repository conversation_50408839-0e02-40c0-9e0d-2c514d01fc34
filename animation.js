// Water Infrastructure 3D Animation
class WaterInfrastructureAnimation {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.animationId = null;
        this.isPlaying = false;
        this.startTime = 0;
        this.duration = 60000; // 60 seconds in milliseconds
        
        // Animation objects
        this.stp = null;
        this.pumpHouse = null;
        this.pipeline = null;
        this.field = null;
        this.waterFlow = null;

        // Water flow particles
        this.waterParticles = [];
        this.particleSystem = null;
        
        // Labels
        this.labels = {
            stp: document.getElementById('stp-label'),
            pump: document.getElementById('pump-label'),
            pipeline: document.getElementById('pipeline-label')
        };
        
        // Controls
        this.playBtn = document.getElementById('play-btn');
        this.pauseBtn = document.getElementById('pause-btn');
        this.resetBtn = document.getElementById('reset-btn');
        this.progressFill = document.getElementById('progress-fill');
        
        this.init();
        this.setupControls();
    }
    
    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Set initial camera position (far left)
        this.camera.position.set(-50, 15, 30);
        this.camera.lookAt(-40, 0, 0);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Add renderer to DOM
        document.getElementById('canvas-container').appendChild(this.renderer.domElement);
        
        // Add lighting
        this.setupLighting();
        
        // Create 3D objects
        this.createSTP();
        this.createPumpHouse();
        this.createPipeline();
        this.createField();
        this.createGround();
        this.createWaterParticleSystem();
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Initial render
        this.renderer.render(this.scene, this.camera);
    }
    
    setupLighting() {
        // Enhanced ambient light for technical clarity
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Main directional light (sun) with improved shadows
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(60, 80, 40);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 4096;
        directionalLight.shadow.mapSize.height = 4096;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        directionalLight.shadow.bias = -0.0001;
        this.scene.add(directionalLight);

        // Technical diagram lighting (from left side)
        const technicalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        technicalLight.position.set(-80, 60, 30);
        technicalLight.castShadow = true;
        technicalLight.shadow.mapSize.width = 2048;
        technicalLight.shadow.mapSize.height = 2048;
        this.scene.add(technicalLight);

        // Fill light for underground visibility
        const undergroundLight = new THREE.DirectionalLight(0x87CEEB, 0.4);
        undergroundLight.position.set(0, -50, 0);
        this.scene.add(undergroundLight);

        // Spot light for STP area
        const stpSpotLight = new THREE.SpotLight(0xffffff, 0.8);
        stpSpotLight.position.set(-45, 30, 20);
        stpSpotLight.target.position.set(-45, 0, 0);
        stpSpotLight.angle = Math.PI / 6;
        stpSpotLight.penumbra = 0.3;
        stpSpotLight.castShadow = true;
        this.scene.add(stpSpotLight);
        this.scene.add(stpSpotLight.target);

        // Spot light for pump house
        const pumpSpotLight = new THREE.SpotLight(0xffffff, 0.7);
        pumpSpotLight.position.set(-15, 25, 15);
        pumpSpotLight.target.position.set(-15, 0, 0);
        pumpSpotLight.angle = Math.PI / 8;
        pumpSpotLight.penumbra = 0.2;
        pumpSpotLight.castShadow = true;
        this.scene.add(pumpSpotLight);
        this.scene.add(pumpSpotLight.target);

        // Spot light for irrigation field
        const fieldSpotLight = new THREE.SpotLight(0xffffff, 0.6);
        fieldSpotLight.position.set(75, 25, 20);
        fieldSpotLight.target.position.set(75, 0, 0);
        fieldSpotLight.angle = Math.PI / 5;
        fieldSpotLight.penumbra = 0.4;
        fieldSpotLight.castShadow = true;
        this.scene.add(fieldSpotLight);
        this.scene.add(fieldSpotLight.target);

        // Hemisphere light for natural outdoor lighting
        const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x8B4513, 0.3);
        this.scene.add(hemisphereLight);
    }
    
    createGround() {
        // Ground plane
        const groundGeometry = new THREE.PlaneGeometry(200, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        // Road/path
        const roadGeometry = new THREE.PlaneGeometry(200, 8);
        const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.rotation.x = -Math.PI / 2;
        road.position.y = -1.9;
        road.position.z = 5;
        this.scene.add(road);
    }
    
    createSTP() {
        this.stp = new THREE.Group();

        // Main STP building with realistic materials
        const buildingGeometry = new THREE.BoxGeometry(15, 10, 12);
        const buildingMaterial = new THREE.MeshPhongMaterial({
            color: 0x8BC34A,
            shininess: 30,
            transparent: false
        });
        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        building.position.set(-45, 5, 0);
        building.castShadow = true;
        building.receiveShadow = true;
        this.stp.add(building);

        // STP roof
        const roofGeometry = new THREE.ConeGeometry(10, 3, 4);
        const roofMaterial = new THREE.MeshPhongMaterial({ color: 0x795548 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.set(-45, 12, 0);
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        this.stp.add(roof);

        // Primary clarifier tanks (realistic circular tanks)
        const tankGeometry = new THREE.CylinderGeometry(4, 4, 3, 32);
        const tankMaterial = new THREE.MeshPhongMaterial({
            color: 0x607D8B,
            shininess: 50
        });

        // Primary clarifier
        const primaryTank = new THREE.Mesh(tankGeometry, tankMaterial);
        primaryTank.position.set(-50, 1.5, -10);
        primaryTank.castShadow = true;
        this.stp.add(primaryTank);

        // Secondary clarifier
        const secondaryTank = new THREE.Mesh(tankGeometry, tankMaterial);
        secondaryTank.position.set(-40, 1.5, -10);
        secondaryTank.castShadow = true;
        this.stp.add(secondaryTank);

        // Aeration tank
        const aerationGeometry = new THREE.BoxGeometry(8, 3, 6);
        const aerationMaterial = new THREE.MeshPhongMaterial({ color: 0x4FC3F7 });
        const aerationTank = new THREE.Mesh(aerationGeometry, aerationMaterial);
        aerationTank.position.set(-45, 1.5, -16);
        aerationTank.castShadow = true;
        this.stp.add(aerationTank);

        // Water in tanks (animated)
        const waterGeometry = new THREE.CylinderGeometry(3.8, 3.8, 0.5, 32);
        const waterMaterial = new THREE.MeshPhongMaterial({
            color: 0x4DD0E1,
            transparent: true,
            opacity: 0.8,
            shininess: 100
        });

        this.stpWater1 = new THREE.Mesh(waterGeometry, waterMaterial);
        this.stpWater1.position.set(-50, 2.8, -10);
        this.stp.add(this.stpWater1);

        this.stpWater2 = new THREE.Mesh(waterGeometry, waterMaterial);
        this.stpWater2.position.set(-40, 2.8, -10);
        this.stp.add(this.stpWater2);

        // Realistic outlet pipe system
        const mainPipeGeometry = new THREE.CylinderGeometry(0.8, 0.8, 12, 16);
        const pipeMaterial = new THREE.MeshPhongMaterial({
            color: 0x37474F,
            shininess: 80
        });
        const outletPipe = new THREE.Mesh(mainPipeGeometry, pipeMaterial);
        outletPipe.rotation.z = Math.PI / 2;
        outletPipe.position.set(-32, 3, 0);
        outletPipe.castShadow = true;
        this.stp.add(outletPipe);

        // Pipe joints and fittings
        const jointGeometry = new THREE.SphereGeometry(1, 16, 16);
        const jointMaterial = new THREE.MeshPhongMaterial({ color: 0x263238 });
        const joint1 = new THREE.Mesh(jointGeometry, jointMaterial);
        joint1.position.set(-38, 3, 0);
        this.stp.add(joint1);

        // Collection sump with realistic design
        const sumpGeometry = new THREE.BoxGeometry(8, 4, 8);
        const sumpMaterial = new THREE.MeshPhongMaterial({
            color: 0x455A64,
            shininess: 30
        });
        const sump = new THREE.Mesh(sumpGeometry, sumpMaterial);
        sump.position.set(-22, 1, 0);
        sump.castShadow = true;
        sump.receiveShadow = true;
        this.stp.add(sump);

        // Water in sump
        const sumpWaterGeometry = new THREE.BoxGeometry(7.5, 1, 7.5);
        const sumpWaterMaterial = new THREE.MeshPhongMaterial({
            color: 0x4DD0E1,
            transparent: true,
            opacity: 0.9,
            shininess: 100
        });
        this.sumpWater = new THREE.Mesh(sumpWaterGeometry, sumpWaterMaterial);
        this.sumpWater.position.set(-22, 2.5, 0);
        this.stp.add(this.sumpWater);

        // Control panel
        const controlGeometry = new THREE.BoxGeometry(2, 3, 1);
        const controlMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
        const controlPanel = new THREE.Mesh(controlGeometry, controlMaterial);
        controlPanel.position.set(-45, 2, 6);
        controlPanel.castShadow = true;
        this.stp.add(controlPanel);

        this.scene.add(this.stp);
    }
    
    createPumpHouse() {
        this.pumpHouse = new THREE.Group();

        // Pump house foundation
        const foundationGeometry = new THREE.BoxGeometry(12, 1, 12);
        const foundationMaterial = new THREE.MeshPhongMaterial({ color: 0x616161 });
        const foundation = new THREE.Mesh(foundationGeometry, foundationMaterial);
        foundation.position.set(-15, 0.5, 0);
        foundation.castShadow = true;
        foundation.receiveShadow = true;
        this.pumpHouse.add(foundation);

        // Pump house building with cross-section view
        const houseGeometry = new THREE.BoxGeometry(10, 8, 10);
        const houseMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFC107,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });
        const house = new THREE.Mesh(houseGeometry, houseMaterial);
        house.position.set(-15, 5, 0);
        house.castShadow = true;
        this.pumpHouse.add(house);

        // Pump house roof
        const roofGeometry = new THREE.ConeGeometry(7, 2, 4);
        const roofMaterial = new THREE.MeshPhongMaterial({ color: 0x8D6E63 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.set(-15, 10, 0);
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        this.pumpHouse.add(roof);

        // Main centrifugal pump
        const pumpBodyGeometry = new THREE.CylinderGeometry(2, 2, 3, 16);
        const pumpMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF5722,
            shininess: 80
        });
        this.mainPump = new THREE.Mesh(pumpBodyGeometry, pumpMaterial);
        this.mainPump.position.set(-15, 3, 0);
        this.mainPump.castShadow = true;
        this.pumpHouse.add(this.mainPump);

        // Pump impeller (visible rotating part)
        const impellerGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.5, 8);
        const impellerMaterial = new THREE.MeshPhongMaterial({ color: 0xE65100 });
        this.pumpImpeller = new THREE.Mesh(impellerGeometry, impellerMaterial);
        this.pumpImpeller.position.set(-15, 3, 0);
        this.pumpHouse.add(this.pumpImpeller);

        // Electric motor
        const motorGeometry = new THREE.CylinderGeometry(1.2, 1.2, 2.5, 16);
        const motorMaterial = new THREE.MeshPhongMaterial({ color: 0x37474F });
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(-15, 6, 0);
        motor.castShadow = true;
        this.pumpHouse.add(motor);

        // Motor cooling fins
        for (let i = 0; i < 8; i++) {
            const finGeometry = new THREE.BoxGeometry(0.1, 2, 0.5);
            const finMaterial = new THREE.MeshPhongMaterial({ color: 0x263238 });
            const fin = new THREE.Mesh(finGeometry, finMaterial);
            const angle = (i / 8) * Math.PI * 2;
            fin.position.set(-15 + Math.cos(angle) * 1.3, 6, Math.sin(angle) * 1.3);
            fin.rotation.y = angle;
            this.pumpHouse.add(fin);
        }

        // Suction pipe
        const suctionGeometry = new THREE.CylinderGeometry(0.8, 0.8, 8, 16);
        const suctionMaterial = new THREE.MeshPhongMaterial({ color: 0x455A64 });
        const suctionPipe = new THREE.Mesh(suctionGeometry, suctionMaterial);
        suctionPipe.rotation.z = Math.PI / 2;
        suctionPipe.position.set(-19, 3, 0);
        suctionPipe.castShadow = true;
        this.pumpHouse.add(suctionPipe);

        // Discharge pipe
        const dischargeGeometry = new THREE.CylinderGeometry(0.8, 0.8, 8, 16);
        const dischargeMaterial = new THREE.MeshPhongMaterial({ color: 0x455A64 });
        const dischargePipe = new THREE.Mesh(dischargeGeometry, dischargeMaterial);
        dischargePipe.rotation.z = Math.PI / 2;
        dischargePipe.position.set(-11, 3, 0);
        dischargePipe.castShadow = true;
        this.pumpHouse.add(dischargePipe);

        // Control valves
        const valveGeometry = new THREE.SphereGeometry(0.6, 16, 16);
        const valveMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });

        const suctionValve = new THREE.Mesh(valveGeometry, valveMaterial);
        suctionValve.position.set(-19, 3, 2);
        this.pumpHouse.add(suctionValve);

        const dischargeValve = new THREE.Mesh(valveGeometry, valveMaterial);
        dischargeValve.position.set(-11, 3, 2);
        this.pumpHouse.add(dischargeValve);

        // Electrical panel
        const panelGeometry = new THREE.BoxGeometry(1.5, 2, 0.3);
        const panelMaterial = new THREE.MeshPhongMaterial({ color: 0x607D8B });
        const electricalPanel = new THREE.Mesh(panelGeometry, panelMaterial);
        electricalPanel.position.set(-20, 4, 4);
        electricalPanel.castShadow = true;
        this.pumpHouse.add(electricalPanel);

        this.scene.add(this.pumpHouse);
    }

    createPipeline() {
        this.pipeline = new THREE.Group();

        // Create soil cross-section to show underground view
        this.createSoilLayers();

        // HDPE pipeline segments with realistic materials
        const pipeGeometry = new THREE.CylinderGeometry(1, 1, 12, 32);
        const pipeMaterial = new THREE.MeshPhongMaterial({
            color: 0x1A1A1A,
            shininess: 60
        });

        // Main pipeline segments from pump house to field
        this.pipeSegments = [];
        for (let i = 0; i < 7; i++) {
            const pipeSegment = new THREE.Mesh(pipeGeometry, pipeMaterial);
            pipeSegment.rotation.z = Math.PI / 2;
            pipeSegment.position.set(-5 + i * 12, -4, 0);
            pipeSegment.castShadow = true;
            this.pipeline.add(pipeSegment);
            this.pipeSegments.push(pipeSegment);
        }

        // Pipe joints and couplings
        const jointGeometry = new THREE.CylinderGeometry(1.2, 1.2, 1, 16);
        const jointMaterial = new THREE.MeshPhongMaterial({ color: 0x424242 });

        for (let i = 0; i < 6; i++) {
            const joint = new THREE.Mesh(jointGeometry, jointMaterial);
            joint.rotation.z = Math.PI / 2;
            joint.position.set(1 + i * 12, -4, 0);
            joint.castShadow = true;
            this.pipeline.add(joint);
        }

        // Pipeline crossing under road (deeper section with protective casing)
        const crossingCasingGeometry = new THREE.CylinderGeometry(1.5, 1.5, 15, 32);
        const casingMaterial = new THREE.MeshPhongMaterial({ color: 0x795548 });
        const protectiveCasing = new THREE.Mesh(crossingCasingGeometry, casingMaterial);
        protectiveCasing.rotation.z = Math.PI / 2;
        protectiveCasing.position.set(25, -6, 5);
        protectiveCasing.castShadow = true;
        this.pipeline.add(protectiveCasing);

        const crossingPipe = new THREE.Mesh(pipeGeometry, pipeMaterial);
        crossingPipe.rotation.z = Math.PI / 2;
        crossingPipe.position.set(25, -6, 5);
        crossingPipe.castShadow = true;
        this.pipeline.add(crossingPipe);

        // Vertical riser pipe to field
        const riserGeometry = new THREE.CylinderGeometry(1, 1, 8, 32);
        const riserPipe = new THREE.Mesh(riserGeometry, pipeMaterial);
        riserPipe.position.set(65, -2, 0);
        riserPipe.castShadow = true;
        this.pipeline.add(riserPipe);

        // Outlet manifold
        const manifoldGeometry = new THREE.BoxGeometry(3, 1, 3);
        const manifoldMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });
        const manifold = new THREE.Mesh(manifoldGeometry, manifoldMaterial);
        manifold.position.set(65, 2.5, 0);
        manifold.castShadow = true;
        this.pipeline.add(manifold);

        // Flow control valve at outlet
        const outletValveGeometry = new THREE.CylinderGeometry(0.8, 0.8, 1.5, 16);
        const outletValveMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
        const outletValve = new THREE.Mesh(outletValveGeometry, outletValveMaterial);
        outletValve.position.set(65, 1.5, 0);
        outletValve.castShadow = true;
        this.pipeline.add(outletValve);

        // Pressure gauge
        const gaugeGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2, 16);
        const gaugeMaterial = new THREE.MeshPhongMaterial({ color: 0xFFFFFF });
        const pressureGauge = new THREE.Mesh(gaugeGeometry, gaugeMaterial);
        pressureGauge.position.set(65, 2, 1.5);
        this.pipeline.add(pressureGauge);

        this.scene.add(this.pipeline);
    }

    createSoilLayers() {
        // Topsoil layer
        const topsoilGeometry = new THREE.BoxGeometry(200, 2, 50);
        const topsoilMaterial = new THREE.MeshPhongMaterial({
            color: 0x8D6E63,
            transparent: true,
            opacity: 0.8
        });
        const topsoil = new THREE.Mesh(topsoilGeometry, topsoilMaterial);
        topsoil.position.set(0, -1, 0);
        topsoil.receiveShadow = true;
        this.scene.add(topsoil);

        // Subsoil layer
        const subsoilGeometry = new THREE.BoxGeometry(200, 4, 50);
        const subsoilMaterial = new THREE.MeshPhongMaterial({
            color: 0x6D4C41,
            transparent: true,
            opacity: 0.7
        });
        const subsoil = new THREE.Mesh(subsoilGeometry, subsoilMaterial);
        subsoil.position.set(0, -4, 0);
        subsoil.receiveShadow = true;
        this.scene.add(subsoil);

        // Bedrock layer
        const bedrockGeometry = new THREE.BoxGeometry(200, 3, 50);
        const bedrockMaterial = new THREE.MeshPhongMaterial({
            color: 0x424242,
            transparent: true,
            opacity: 0.6
        });
        const bedrock = new THREE.Mesh(bedrockGeometry, bedrockMaterial);
        bedrock.position.set(0, -7.5, 0);
        bedrock.receiveShadow = true;
        this.scene.add(bedrock);
    }

    createWaterParticleSystem() {
        // Create particle geometry for water flow
        const particleCount = 200;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        for (let i = 0; i < particleCount; i++) {
            // Initialize particles along the water path
            positions[i * 3] = -50 + Math.random() * 120; // x
            positions[i * 3 + 1] = -2 + Math.random() * 4; // y
            positions[i * 3 + 2] = -2 + Math.random() * 4; // z

            // Blue water color with variations
            colors[i * 3] = 0.2 + Math.random() * 0.3; // r
            colors[i * 3 + 1] = 0.6 + Math.random() * 0.4; // g
            colors[i * 3 + 2] = 0.8 + Math.random() * 0.2; // b

            sizes[i] = 0.1 + Math.random() * 0.2;
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Particle material
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.3,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending
        });

        this.particleSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(this.particleSystem);

        // Store reference to positions for animation
        this.particlePositions = positions;
    }

    createField() {
        this.field = new THREE.Group();

        // Agricultural field base with realistic texture
        const fieldGeometry = new THREE.PlaneGeometry(35, 30);
        const fieldMaterial = new THREE.MeshPhongMaterial({
            color: 0x4CAF50,
            shininess: 10
        });
        const fieldBase = new THREE.Mesh(fieldGeometry, fieldMaterial);
        fieldBase.rotation.x = -Math.PI / 2;
        fieldBase.position.set(75, -1.7, 0);
        fieldBase.receiveShadow = true;
        this.field.add(fieldBase);

        // Crop rows with realistic plants
        this.crops = [];
        for (let row = 0; row < 6; row++) {
            for (let plant = 0; plant < 8; plant++) {
                // Plant stem
                const stemGeometry = new THREE.CylinderGeometry(0.1, 0.15, 2.5, 8);
                const stemMaterial = new THREE.MeshPhongMaterial({ color: 0x689F38 });
                const stem = new THREE.Mesh(stemGeometry, stemMaterial);
                stem.position.set(60 + row * 5, 1.25, -12 + plant * 3);
                stem.castShadow = true;
                this.field.add(stem);

                // Plant leaves
                for (let i = 0; i < 4; i++) {
                    const leafGeometry = new THREE.PlaneGeometry(1.5, 0.8);
                    const leafMaterial = new THREE.MeshPhongMaterial({
                        color: 0x8BC34A,
                        side: THREE.DoubleSide
                    });
                    const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
                    leaf.position.set(
                        60 + row * 5 + (Math.random() - 0.5) * 0.5,
                        1.5 + i * 0.3,
                        -12 + plant * 3 + (Math.random() - 0.5) * 0.5
                    );
                    leaf.rotation.y = (Math.random() - 0.5) * Math.PI;
                    leaf.rotation.z = (Math.random() - 0.5) * 0.3;
                    leaf.castShadow = true;
                    this.field.add(leaf);
                }

                this.crops.push(stem);
            }
        }

        // Irrigation system
        this.createIrrigationSystem();

        this.scene.add(this.field);
    }

    createIrrigationSystem() {
        // Main distribution header
        const headerGeometry = new THREE.CylinderGeometry(0.3, 0.3, 30, 16);
        const headerMaterial = new THREE.MeshPhongMaterial({ color: 0x37474F });
        const mainHeader = new THREE.Mesh(headerGeometry, headerMaterial);
        mainHeader.rotation.x = Math.PI / 2;
        mainHeader.position.set(75, 0.3, 0);
        mainHeader.castShadow = true;
        this.field.add(mainHeader);

        // Drip irrigation laterals
        this.irrigationLines = [];
        for (let i = 0; i < 6; i++) {
            const lineGeometry = new THREE.CylinderGeometry(0.08, 0.08, 24, 12);
            const lineMaterial = new THREE.MeshPhongMaterial({ color: 0x1A1A1A });
            const line = new THREE.Mesh(lineGeometry, lineMaterial);
            line.rotation.x = Math.PI / 2;
            line.position.set(60 + i * 5, 0.15, 0);
            line.castShadow = true;
            this.field.add(line);
            this.irrigationLines.push(line);
        }

        // Drip emitters
        this.drippers = [];
        for (let row = 0; row < 6; row++) {
            for (let emitter = 0; emitter < 8; emitter++) {
                const dripperGeometry = new THREE.SphereGeometry(0.05, 8, 8);
                const dripperMaterial = new THREE.MeshPhongMaterial({ color: 0x2196F3 });
                const dripper = new THREE.Mesh(dripperGeometry, dripperMaterial);
                dripper.position.set(60 + row * 5, 0.1, -12 + emitter * 3);
                this.field.add(dripper);
                this.drippers.push(dripper);
            }
        }

        // Micro-sprinklers for better coverage
        this.sprinklers = [];
        for (let x = 0; x < 3; x++) {
            for (let z = 0; z < 4; z++) {
                // Sprinkler riser
                const riserGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.8, 8);
                const riserMaterial = new THREE.MeshPhongMaterial({ color: 0x424242 });
                const riser = new THREE.Mesh(riserGeometry, riserMaterial);
                riser.position.set(62 + x * 10, 0.4, -9 + z * 6);
                this.field.add(riser);

                // Sprinkler head
                const sprinklerGeometry = new THREE.CylinderGeometry(0.15, 0.1, 0.3, 12);
                const sprinklerMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
                const sprinkler = new THREE.Mesh(sprinklerGeometry, sprinklerMaterial);
                sprinkler.position.set(62 + x * 10, 0.95, -9 + z * 6);
                sprinkler.castShadow = true;
                this.field.add(sprinkler);
                this.sprinklers.push(sprinkler);

                // Sprinkler nozzle
                const nozzleGeometry = new THREE.SphereGeometry(0.08, 8, 8);
                const nozzleMaterial = new THREE.MeshPhongMaterial({ color: 0x2196F3 });
                const nozzle = new THREE.Mesh(nozzleGeometry, nozzleMaterial);
                nozzle.position.set(62 + x * 10, 1.1, -9 + z * 6);
                this.field.add(nozzle);
            }
        }

        // Control valves for irrigation zones
        for (let i = 0; i < 3; i++) {
            const valveGeometry = new THREE.BoxGeometry(0.5, 0.3, 0.5);
            const valveMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });
            const valve = new THREE.Mesh(valveGeometry, valveMaterial);
            valve.position.set(58, 0.3, -6 + i * 6);
            valve.castShadow = true;
            this.field.add(valve);
        }

        // Pressure regulator
        const regulatorGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.6, 12);
        const regulatorMaterial = new THREE.MeshPhongMaterial({ color: 0xFF5722 });
        const regulator = new THREE.Mesh(regulatorGeometry, regulatorMaterial);
        regulator.position.set(67, 0.5, 2);
        regulator.castShadow = true;
        this.field.add(regulator);
    }

    setupControls() {
        this.playBtn.addEventListener('click', () => this.startAnimation());
        this.pauseBtn.addEventListener('click', () => this.pauseAnimation());
        this.resetBtn.addEventListener('click', () => this.resetAnimation());
    }

    startAnimation() {
        if (!this.isPlaying) {
            this.isPlaying = true;
            this.startTime = Date.now();
            this.playBtn.disabled = true;
            this.pauseBtn.disabled = false;
            this.animate();
        }
    }

    pauseAnimation() {
        this.isPlaying = false;
        this.playBtn.disabled = false;
        this.pauseBtn.disabled = true;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    resetAnimation() {
        this.pauseAnimation();
        this.startTime = 0;
        this.progressFill.style.width = '0%';

        // Reset camera position
        this.camera.position.set(-50, 15, 30);
        this.camera.lookAt(-40, 0, 0);

        // Hide all labels
        Object.values(this.labels).forEach(label => {
            label.classList.remove('show');
        });

        this.renderer.render(this.scene, this.camera);
    }

    animate() {
        if (!this.isPlaying) return;

        const currentTime = Date.now();
        const elapsed = currentTime - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);

        // Update progress bar
        this.progressFill.style.width = (progress * 100) + '%';

        // Camera animation (rightward pan)
        this.updateCamera(progress);

        // Label timing
        this.updateLabels(progress);

        // Water flow animation
        this.updateWaterFlow(progress);

        // Render the scene
        this.renderer.render(this.scene, this.camera);

        // Continue animation or stop at end
        if (progress < 1) {
            this.animationId = requestAnimationFrame(() => this.animate());
        } else {
            this.pauseAnimation();
        }
    }

    updateCamera(progress) {
        const timeInSeconds = progress * 60;

        // Phase-based camera movement over 60 seconds
        let cameraX, cameraY, cameraZ, lookAtX, lookAtY, lookAtZ;

        if (timeInSeconds <= 15) {
            // Phase 1: Pre-treatment and sewage collection (0-15s)
            const phaseProgress = timeInSeconds / 15;
            cameraX = -70 + phaseProgress * 20; // Move from -70 to -50
            cameraY = 20 + Math.sin(phaseProgress * Math.PI) * 3;
            cameraZ = 40;
            lookAtX = cameraX + 15;
            lookAtY = 0;
            lookAtZ = 0;
        } else if (timeInSeconds <= 30) {
            // Phase 2: STP treatment process (15-30s)
            const phaseProgress = (timeInSeconds - 15) / 15;
            cameraX = -50 + phaseProgress * 10; // Move from -50 to -40
            cameraY = 15 + Math.sin(phaseProgress * Math.PI * 2) * 5; // More dynamic movement
            cameraZ = 35 - phaseProgress * 10; // Move closer for detail
            lookAtX = -45;
            lookAtY = 2;
            lookAtZ = 0;
        } else if (timeInSeconds <= 45) {
            // Phase 3: Pump house and pipeline (30-45s)
            const phaseProgress = (timeInSeconds - 30) / 15;
            cameraX = -40 + phaseProgress * 60; // Move from -40 to 20
            cameraY = 12 + Math.sin(phaseProgress * Math.PI) * 3;
            cameraZ = 25 + phaseProgress * 15; // Move back for wider view
            lookAtX = cameraX + 20;
            lookAtY = -2; // Look at underground pipeline
            lookAtZ = 0;
        } else {
            // Phase 4: Irrigation and field (45-60s)
            const phaseProgress = (timeInSeconds - 45) / 15;
            cameraX = 20 + phaseProgress * 50; // Move from 20 to 70
            cameraY = 18 + Math.sin(phaseProgress * Math.PI * 3) * 4; // Dynamic field view
            cameraZ = 40 - phaseProgress * 10; // Move closer to field
            lookAtX = 75;
            lookAtY = 1;
            lookAtZ = 0;
        }

        // Apply smooth easing
        const easedProgress = this.easeInOutCubic(progress);

        this.camera.position.x = cameraX;
        this.camera.position.y = cameraY;
        this.camera.position.z = cameraZ;
        this.camera.lookAt(lookAtX, lookAtY, lookAtZ);
    }

    updateLabels(progress) {
        const timeInSeconds = progress * 8;

        // STP label (0-2 seconds)
        if (timeInSeconds >= 0 && timeInSeconds <= 2.5) {
            this.labels.stp.classList.add('show');
        } else {
            this.labels.stp.classList.remove('show');
        }

        // Pump house label (2-4 seconds)
        if (timeInSeconds >= 1.8 && timeInSeconds <= 4.5) {
            this.labels.pump.classList.add('show');
        } else {
            this.labels.pump.classList.remove('show');
        }

        // Pipeline label (4-7 seconds)
        if (timeInSeconds >= 3.8 && timeInSeconds <= 7.5) {
            this.labels.pipeline.classList.add('show');
        } else {
            this.labels.pipeline.classList.remove('show');
        }
    }

    updateWaterFlow(progress) {
        const timeInSeconds = progress * 8;

        // Animate water flow through system
        if (timeInSeconds >= 1) {
            this.animateWaterInSTP();
        }

        if (timeInSeconds >= 2.5) {
            this.animatePumpOperation();
        }

        if (timeInSeconds >= 4) {
            this.animateWaterInPipeline();
        }

        if (timeInSeconds >= 7) {
            this.animateIrrigation();
        }
    }

    animateWaterInSTP() {
        // Animate water surface in STP tanks
        if (this.stpWater1 && this.stpWater2) {
            const time = Date.now() * 0.001;
            this.stpWater1.position.y = 2.8 + Math.sin(time * 2) * 0.05;
            this.stpWater2.position.y = 2.8 + Math.sin(time * 2.5) * 0.05;

            // Animate water color to show treatment process
            this.stpWater1.material.color.setHSL(0.55, 0.8, 0.6 + Math.sin(time) * 0.1);
            this.stpWater2.material.color.setHSL(0.55, 0.9, 0.7 + Math.sin(time * 1.2) * 0.1);
        }

        // Animate sump water
        if (this.sumpWater) {
            const time = Date.now() * 0.001;
            this.sumpWater.position.y = 2.5 + Math.sin(time * 3) * 0.03;
        }
    }

    animatePumpOperation() {
        // Rotate pump impeller to show operation
        if (this.pumpImpeller) {
            this.pumpImpeller.rotation.y += 0.3;
        }

        // Animate main pump vibration
        if (this.mainPump) {
            const time = Date.now() * 0.01;
            this.mainPump.position.y = 3 + Math.sin(time * 5) * 0.02;
        }
    }

    animateWaterInPipeline() {
        // Animate water particles flowing through pipeline
        if (this.particleSystem && this.particlePositions) {
            const time = Date.now() * 0.001;
            const positions = this.particlePositions;

            for (let i = 0; i < positions.length / 3; i++) {
                // Move particles along the pipeline path
                positions[i * 3] += 0.5; // Move rightward

                // Reset particles that have moved past the field
                if (positions[i * 3] > 80) {
                    positions[i * 3] = -50;
                    positions[i * 3 + 1] = -4 + Math.random() * 2;
                    positions[i * 3 + 2] = -1 + Math.random() * 2;
                }

                // Add some vertical movement for realism
                positions[i * 3 + 1] += Math.sin(time + i * 0.1) * 0.01;
            }

            this.particleSystem.geometry.attributes.position.needsUpdate = true;
        }

        // Animate pipeline pressure (subtle color changes)
        if (this.pipeSegments) {
            const time = Date.now() * 0.001;
            this.pipeSegments.forEach((segment, index) => {
                const intensity = 0.1 + 0.05 * Math.sin(time * 2 + index * 0.5);
                segment.material.emissive.setRGB(0, 0, intensity);
            });
        }
    }

    animateIrrigation() {
        // Animate sprinkler rotation
        if (this.sprinklers) {
            this.sprinklers.forEach((sprinkler, index) => {
                sprinkler.rotation.y += 0.05 + index * 0.01;
            });
        }

        // Animate drip emitters
        if (this.drippers) {
            const time = Date.now() * 0.001;
            this.drippers.forEach((dripper, index) => {
                const scale = 1 + 0.3 * Math.sin(time * 4 + index * 0.2);
                dripper.scale.setScalar(scale);

                // Create water droplet effect
                if (Math.random() < 0.1) {
                    this.createWaterDroplet(dripper.position);
                }
            });
        }

        // Animate crops growing/swaying
        if (this.crops) {
            const time = Date.now() * 0.001;
            this.crops.forEach((crop, index) => {
                crop.rotation.z = Math.sin(time + index * 0.1) * 0.05;
                crop.scale.y = 1 + 0.02 * Math.sin(time * 0.5 + index * 0.05);
            });
        }
    }

    createWaterDroplet(position) {
        // Create temporary water droplet
        const dropletGeometry = new THREE.SphereGeometry(0.02, 8, 8);
        const dropletMaterial = new THREE.MeshPhongMaterial({
            color: 0x4DD0E1,
            transparent: true,
            opacity: 0.8
        });
        const droplet = new THREE.Mesh(dropletGeometry, dropletMaterial);
        droplet.position.copy(position);
        droplet.position.y -= 0.1;

        this.scene.add(droplet);

        // Animate droplet falling
        const fallAnimation = () => {
            droplet.position.y -= 0.05;
            droplet.material.opacity -= 0.02;

            if (droplet.position.y > -2 && droplet.material.opacity > 0) {
                requestAnimationFrame(fallAnimation);
            } else {
                this.scene.remove(droplet);
            }
        };

        fallAnimation();
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// Initialize the animation when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new WaterInfrastructureAnimation();
});
