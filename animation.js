// Water Infrastructure 3D Animation
class WaterInfrastructureAnimation {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.animationId = null;
        this.isPlaying = false;
        this.startTime = 0;
        this.duration = 60000; // 60 seconds in milliseconds

        // Project specifications
        this.projectData = {
            stpLocation: "STP Uchana, Haryana",
            village: "Village Kasoon",
            discharge: "4.5 MLD",
            distance: "8 Km",
            mainPipeDia: "300 mm HDPE",
            canalPipeDia: "200 mm HDPE",
            irrigationArea: "2500 acres",
            hydrantCoverage: "4 acres per hydrant"
        };
        
        // Animation objects
        this.stp = null;
        this.pumpHouse = null;
        this.pipeline = null;
        this.field = null;
        this.waterFlow = null;

        // Water flow particles
        this.waterParticles = [];
        this.particleSystem = null;

        // Audio system
        this.audioContext = null;
        this.audioNarration = [];
        this.currentAudio = null;
        
        // Labels
        this.labels = {
            stp: document.getElementById('stp-label'),
            pump: document.getElementById('pump-label'),
            pipeline: document.getElementById('pipeline-label'),
            storage: document.getElementById('storage-label'),
            canal: document.getElementById('canal-label'),
            network: document.getElementById('network-label'),
            hydrant: document.getElementById('hydrant-label'),
            irrigation: document.getElementById('irrigation-label')
        };
        
        // Controls
        this.playBtn = document.getElementById('play-btn');
        this.pauseBtn = document.getElementById('pause-btn');
        this.resetBtn = document.getElementById('reset-btn');
        this.progressFill = document.getElementById('progress-fill');
        this.timeline = document.getElementById('timeline');
        this.audioToggleBtn = document.getElementById('audio-toggle');

        // Debug: Check if elements are found
        console.log('DOM Elements found:', {
            playBtn: !!this.playBtn,
            pauseBtn: !!this.pauseBtn,
            resetBtn: !!this.resetBtn,
            progressFill: !!this.progressFill,
            timeline: !!this.timeline,
            audioToggleBtn: !!this.audioToggleBtn
        });

        // Audio settings
        this.audioEnabled = true;
        
        this.init();
        this.setupControls();
    }
    
    init() {
        console.log('Initializing 3D animation...');
        try {
            // Create scene
            this.scene = new THREE.Scene();
            this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Set initial camera position at STP Uchana
        this.camera.position.set(-80, 25, 50);
        this.camera.lookAt(-60, 3, 0);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // Add renderer to DOM
        document.getElementById('canvas-container').appendChild(this.renderer.domElement);
        
        // Add lighting
        this.setupLighting();
        
        // Create 3D objects
        this.createSTP();
        this.createPumpHouse();
        this.createPipeline();
        this.createField();
        this.createGround();
        this.createEnvironmentalEffects();
        this.createWaterParticleSystem();
        this.initializeAudio();
        
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Initial render
        this.renderer.render(this.scene, this.camera);
        console.log('3D animation initialized successfully');

        } catch (error) {
            console.error('Error initializing animation:', error);
            alert('Error loading 3D animation. Please refresh the page.');
        }
    }
    
    setupLighting() {
        // Enhanced ambient light for technical clarity
        const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
        this.scene.add(ambientLight);

        // Add fog for atmospheric effect
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 300);

        // Main directional light (sun) with improved shadows
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(60, 80, 40);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 4096;
        directionalLight.shadow.mapSize.height = 4096;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        directionalLight.shadow.bias = -0.0001;
        this.scene.add(directionalLight);

        // Technical diagram lighting (from left side)
        const technicalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        technicalLight.position.set(-80, 60, 30);
        technicalLight.castShadow = true;
        technicalLight.shadow.mapSize.width = 2048;
        technicalLight.shadow.mapSize.height = 2048;
        this.scene.add(technicalLight);

        // Fill light for underground visibility
        const undergroundLight = new THREE.DirectionalLight(0x87CEEB, 0.4);
        undergroundLight.position.set(0, -50, 0);
        this.scene.add(undergroundLight);

        // Spot light for STP area
        const stpSpotLight = new THREE.SpotLight(0xffffff, 0.8);
        stpSpotLight.position.set(-45, 30, 20);
        stpSpotLight.target.position.set(-45, 0, 0);
        stpSpotLight.angle = Math.PI / 6;
        stpSpotLight.penumbra = 0.3;
        stpSpotLight.castShadow = true;
        this.scene.add(stpSpotLight);
        this.scene.add(stpSpotLight.target);

        // Spot light for pump house
        const pumpSpotLight = new THREE.SpotLight(0xffffff, 0.7);
        pumpSpotLight.position.set(-15, 25, 15);
        pumpSpotLight.target.position.set(-15, 0, 0);
        pumpSpotLight.angle = Math.PI / 8;
        pumpSpotLight.penumbra = 0.2;
        pumpSpotLight.castShadow = true;
        this.scene.add(pumpSpotLight);
        this.scene.add(pumpSpotLight.target);

        // Spot light for irrigation field
        const fieldSpotLight = new THREE.SpotLight(0xffffff, 0.6);
        fieldSpotLight.position.set(75, 25, 20);
        fieldSpotLight.target.position.set(75, 0, 0);
        fieldSpotLight.angle = Math.PI / 5;
        fieldSpotLight.penumbra = 0.4;
        fieldSpotLight.castShadow = true;
        this.scene.add(fieldSpotLight);
        this.scene.add(fieldSpotLight.target);

        // Hemisphere light for natural outdoor lighting
        const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x8B4513, 0.3);
        this.scene.add(hemisphereLight);
    }
    
    createGround() {
        // Ground plane
        const groundGeometry = new THREE.PlaneGeometry(200, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        
        // Road/path
        const roadGeometry = new THREE.PlaneGeometry(200, 8);
        const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.rotation.x = -Math.PI / 2;
        road.position.y = -1.9;
        road.position.z = 5;
        this.scene.add(road);
    }
    
    createSTP() {
        this.stp = new THREE.Group();

        // STP Uchana main building complex
        const buildingGeometry = new THREE.BoxGeometry(20, 12, 15);
        const buildingMaterial = new THREE.MeshPhongMaterial({
            color: 0x8BC34A,
            shininess: 30,
            transparent: false
        });
        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        building.position.set(-60, 6, 0);
        building.castShadow = true;
        building.receiveShadow = true;
        this.stp.add(building);

        // STP Uchana signboard
        const signGeometry = new THREE.PlaneGeometry(8, 3);
        const signMaterial = new THREE.MeshPhongMaterial({ color: 0xFFFFFF });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(-60, 14, 8);
        this.stp.add(sign);

        // STP roof
        const roofGeometry = new THREE.ConeGeometry(12, 4, 4);
        const roofMaterial = new THREE.MeshPhongMaterial({ color: 0x795548 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.set(-60, 14, 0);
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        this.stp.add(roof);

        // STP Uchana treatment tanks (4.5 MLD capacity)
        const tankGeometry = new THREE.CylinderGeometry(5, 5, 4, 32);
        const tankMaterial = new THREE.MeshPhongMaterial({
            color: 0x607D8B,
            shininess: 50
        });

        // Primary clarifier tank
        const primaryTank = new THREE.Mesh(tankGeometry, tankMaterial);
        primaryTank.position.set(-65, 2, -12);
        primaryTank.castShadow = true;
        this.stp.add(primaryTank);

        // Secondary clarifier tank
        const secondaryTank = new THREE.Mesh(tankGeometry, tankMaterial);
        secondaryTank.position.set(-55, 2, -12);
        secondaryTank.castShadow = true;
        this.stp.add(secondaryTank);

        // Tertiary treatment tank
        const tertiaryTank = new THREE.Mesh(tankGeometry, tankMaterial);
        tertiaryTank.position.set(-45, 2, -12);
        tertiaryTank.castShadow = true;
        this.stp.add(tertiaryTank);

        // Aeration tank
        const aerationGeometry = new THREE.BoxGeometry(8, 3, 6);
        const aerationMaterial = new THREE.MeshPhongMaterial({ color: 0x4FC3F7 });
        const aerationTank = new THREE.Mesh(aerationGeometry, aerationMaterial);
        aerationTank.position.set(-45, 1.5, -16);
        aerationTank.castShadow = true;
        this.stp.add(aerationTank);

        // Water in tanks (animated)
        const waterGeometry = new THREE.CylinderGeometry(3.8, 3.8, 0.5, 32);
        const waterMaterial = new THREE.MeshPhongMaterial({
            color: 0x4DD0E1,
            transparent: true,
            opacity: 0.8,
            shininess: 100
        });

        this.stpWater1 = new THREE.Mesh(waterGeometry, waterMaterial);
        this.stpWater1.position.set(-50, 2.8, -10);
        this.stp.add(this.stpWater1);

        this.stpWater2 = new THREE.Mesh(waterGeometry, waterMaterial);
        this.stpWater2.position.set(-40, 2.8, -10);
        this.stp.add(this.stpWater2);

        // STP outlet system (4.5 MLD discharge)
        const outletPipeGeometry = new THREE.CylinderGeometry(1.5, 1.5, 15, 32); // 300mm diameter
        const pipeMaterial = new THREE.MeshPhongMaterial({
            color: 0x1A1A1A, // Black HDPE color
            shininess: 80
        });
        const outletPipe = new THREE.Mesh(outletPipeGeometry, pipeMaterial);
        outletPipe.rotation.z = Math.PI / 2;
        outletPipe.position.set(-30, 4, 0);
        outletPipe.castShadow = true;
        this.stp.add(outletPipe);

        // Flow measurement chamber
        const measurementChamberGeometry = new THREE.BoxGeometry(4, 3, 4);
        const chamberMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
        const measurementChamber = new THREE.Mesh(measurementChamberGeometry, chamberMaterial);
        measurementChamber.position.set(-35, 2.5, 0);
        measurementChamber.castShadow = true;
        this.stp.add(measurementChamber);

        // 4.5 MLD flow indicator
        const flowIndicatorGeometry = new THREE.PlaneGeometry(3, 1);
        const flowIndicatorMaterial = new THREE.MeshPhongMaterial({ color: 0xFFFFFF });
        const flowIndicator = new THREE.Mesh(flowIndicatorGeometry, flowIndicatorMaterial);
        flowIndicator.position.set(-35, 5, 2);
        this.stp.add(flowIndicator);

        // Water in sump
        const sumpWaterGeometry = new THREE.BoxGeometry(7.5, 1, 7.5);
        const sumpWaterMaterial = new THREE.MeshPhongMaterial({
            color: 0x4DD0E1,
            transparent: true,
            opacity: 0.9,
            shininess: 100
        });
        this.sumpWater = new THREE.Mesh(sumpWaterGeometry, sumpWaterMaterial);
        this.sumpWater.position.set(-22, 2.5, 0);
        this.stp.add(this.sumpWater);

        // Control panel
        const controlGeometry = new THREE.BoxGeometry(2, 3, 1);
        const controlMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
        const controlPanel = new THREE.Mesh(controlGeometry, controlMaterial);
        controlPanel.position.set(-45, 2, 6);
        controlPanel.castShadow = true;
        this.stp.add(controlPanel);

        this.scene.add(this.stp);
    }
    
    createPumpHouse() {
        this.pumpHouse = new THREE.Group();

        // Pump house at STP outlet (for 8km transmission)
        const foundationGeometry = new THREE.BoxGeometry(15, 1.5, 15);
        const foundationMaterial = new THREE.MeshPhongMaterial({ color: 0x616161 });
        const foundation = new THREE.Mesh(foundationGeometry, foundationMaterial);
        foundation.position.set(-20, 0.75, 0);
        foundation.castShadow = true;
        foundation.receiveShadow = true;
        this.pumpHouse.add(foundation);

        // Pump house building with cross-section view
        const houseGeometry = new THREE.BoxGeometry(10, 8, 10);
        const houseMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFC107,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });
        const house = new THREE.Mesh(houseGeometry, houseMaterial);
        house.position.set(-15, 5, 0);
        house.castShadow = true;
        this.pumpHouse.add(house);

        // Pump house roof
        const roofGeometry = new THREE.ConeGeometry(7, 2, 4);
        const roofMaterial = new THREE.MeshPhongMaterial({ color: 0x8D6E63 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.set(-15, 10, 0);
        roof.rotation.y = Math.PI / 4;
        roof.castShadow = true;
        this.pumpHouse.add(roof);

        // Main centrifugal pump
        const pumpBodyGeometry = new THREE.CylinderGeometry(2, 2, 3, 16);
        const pumpMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF5722,
            shininess: 80
        });
        this.mainPump = new THREE.Mesh(pumpBodyGeometry, pumpMaterial);
        this.mainPump.position.set(-15, 3, 0);
        this.mainPump.castShadow = true;
        this.pumpHouse.add(this.mainPump);

        // Pump impeller (visible rotating part)
        const impellerGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.5, 8);
        const impellerMaterial = new THREE.MeshPhongMaterial({ color: 0xE65100 });
        this.pumpImpeller = new THREE.Mesh(impellerGeometry, impellerMaterial);
        this.pumpImpeller.position.set(-15, 3, 0);
        this.pumpHouse.add(this.pumpImpeller);

        // Electric motor
        const motorGeometry = new THREE.CylinderGeometry(1.2, 1.2, 2.5, 16);
        const motorMaterial = new THREE.MeshPhongMaterial({ color: 0x37474F });
        const motor = new THREE.Mesh(motorGeometry, motorMaterial);
        motor.position.set(-15, 6, 0);
        motor.castShadow = true;
        this.pumpHouse.add(motor);

        // Motor cooling fins
        for (let i = 0; i < 8; i++) {
            const finGeometry = new THREE.BoxGeometry(0.1, 2, 0.5);
            const finMaterial = new THREE.MeshPhongMaterial({ color: 0x263238 });
            const fin = new THREE.Mesh(finGeometry, finMaterial);
            const angle = (i / 8) * Math.PI * 2;
            fin.position.set(-15 + Math.cos(angle) * 1.3, 6, Math.sin(angle) * 1.3);
            fin.rotation.y = angle;
            this.pumpHouse.add(fin);
        }

        // Suction pipe
        const suctionGeometry = new THREE.CylinderGeometry(0.8, 0.8, 8, 16);
        const suctionMaterial = new THREE.MeshPhongMaterial({ color: 0x455A64 });
        const suctionPipe = new THREE.Mesh(suctionGeometry, suctionMaterial);
        suctionPipe.rotation.z = Math.PI / 2;
        suctionPipe.position.set(-19, 3, 0);
        suctionPipe.castShadow = true;
        this.pumpHouse.add(suctionPipe);

        // Discharge pipe
        const dischargeGeometry = new THREE.CylinderGeometry(0.8, 0.8, 8, 16);
        const dischargeMaterial = new THREE.MeshPhongMaterial({ color: 0x455A64 });
        const dischargePipe = new THREE.Mesh(dischargeGeometry, dischargeMaterial);
        dischargePipe.rotation.z = Math.PI / 2;
        dischargePipe.position.set(-11, 3, 0);
        dischargePipe.castShadow = true;
        this.pumpHouse.add(dischargePipe);

        // Control valves
        const valveGeometry = new THREE.SphereGeometry(0.6, 16, 16);
        const valveMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });

        const suctionValve = new THREE.Mesh(valveGeometry, valveMaterial);
        suctionValve.position.set(-19, 3, 2);
        this.pumpHouse.add(suctionValve);

        const dischargeValve = new THREE.Mesh(valveGeometry, valveMaterial);
        dischargeValve.position.set(-11, 3, 2);
        this.pumpHouse.add(dischargeValve);

        // Electrical panel
        const panelGeometry = new THREE.BoxGeometry(1.5, 2, 0.3);
        const panelMaterial = new THREE.MeshPhongMaterial({ color: 0x607D8B });
        const electricalPanel = new THREE.Mesh(panelGeometry, panelMaterial);
        electricalPanel.position.set(-20, 4, 4);
        electricalPanel.castShadow = true;
        this.pumpHouse.add(electricalPanel);

        this.scene.add(this.pumpHouse);
    }

    createPipeline() {
        this.pipeline = new THREE.Group();

        // Create soil cross-section to show underground view
        this.createSoilLayers();

        // 300mm HDPE pipeline (8km from STP Uchana to Village Kasoon)
        const mainPipeGeometry = new THREE.CylinderGeometry(1.5, 1.5, 15, 32); // 300mm diameter
        const pipeMaterial = new THREE.MeshPhongMaterial({
            color: 0x1A1A1A, // Black HDPE
            shininess: 60
        });

        // Main transmission pipeline segments (representing 8km distance)
        this.pipeSegments = [];
        for (let i = 0; i < 12; i++) { // More segments to represent 8km
            const pipeSegment = new THREE.Mesh(mainPipeGeometry, pipeMaterial);
            pipeSegment.rotation.z = Math.PI / 2;
            pipeSegment.position.set(-10 + i * 15, -5, 0); // Deeper underground
            pipeSegment.castShadow = true;
            this.pipeline.add(pipeSegment);
            this.pipeSegments.push(pipeSegment);
        }

        // Pipe joints and couplings
        const jointGeometry = new THREE.CylinderGeometry(1.2, 1.2, 1, 16);
        const jointMaterial = new THREE.MeshPhongMaterial({ color: 0x424242 });

        for (let i = 0; i < 6; i++) {
            const joint = new THREE.Mesh(jointGeometry, jointMaterial);
            joint.rotation.z = Math.PI / 2;
            joint.position.set(1 + i * 12, -4, 0);
            joint.castShadow = true;
            this.pipeline.add(joint);
        }

        // Pipeline crossing under road (deeper section with protective casing)
        const crossingCasingGeometry = new THREE.CylinderGeometry(1.5, 1.5, 15, 32);
        const casingMaterial = new THREE.MeshPhongMaterial({ color: 0x795548 });
        const protectiveCasing = new THREE.Mesh(crossingCasingGeometry, casingMaterial);
        protectiveCasing.rotation.z = Math.PI / 2;
        protectiveCasing.position.set(25, -6, 5);
        protectiveCasing.castShadow = true;
        this.pipeline.add(protectiveCasing);

        const crossingPipe = new THREE.Mesh(mainPipeGeometry, pipeMaterial);
        crossingPipe.rotation.z = Math.PI / 2;
        crossingPipe.position.set(25, -6, 5);
        crossingPipe.castShadow = true;
        this.pipeline.add(crossingPipe);

        // Vertical riser pipe to field
        const riserGeometry = new THREE.CylinderGeometry(1, 1, 8, 32);
        const riserPipe = new THREE.Mesh(riserGeometry, pipeMaterial);
        riserPipe.position.set(65, -2, 0);
        riserPipe.castShadow = true;
        this.pipeline.add(riserPipe);

        // Outlet manifold
        const manifoldGeometry = new THREE.BoxGeometry(3, 1, 3);
        const manifoldMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });
        const manifold = new THREE.Mesh(manifoldGeometry, manifoldMaterial);
        manifold.position.set(65, 2.5, 0);
        manifold.castShadow = true;
        this.pipeline.add(manifold);

        // Flow control valve at outlet
        const outletValveGeometry = new THREE.CylinderGeometry(0.8, 0.8, 1.5, 16);
        const outletValveMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
        const outletValve = new THREE.Mesh(outletValveGeometry, outletValveMaterial);
        outletValve.position.set(65, 1.5, 0);
        outletValve.castShadow = true;
        this.pipeline.add(outletValve);

        // Village Kasoon water storage tank (on Panchayat land)
        this.createVillageKasoonStorage();

        // Canal tail water collection system
        this.createCanalTailSystem();

        this.scene.add(this.pipeline);
    }

    createVillageKasoonStorage() {
        // Main storage tank at Village Kasoon
        const tankGeometry = new THREE.CylinderGeometry(8, 8, 6, 32);
        const tankMaterial = new THREE.MeshPhongMaterial({
            color: 0x607D8B,
            shininess: 50
        });
        const storageTank = new THREE.Mesh(tankGeometry, tankMaterial);
        storageTank.position.set(150, 3, 0); // Village Kasoon location
        storageTank.castShadow = true;
        this.pipeline.add(storageTank);

        // Tank foundation
        const foundationGeometry = new THREE.CylinderGeometry(9, 9, 1, 32);
        const foundationMaterial = new THREE.MeshPhongMaterial({ color: 0x616161 });
        const foundation = new THREE.Mesh(foundationGeometry, foundationMaterial);
        foundation.position.set(150, 0.5, 0);
        foundation.castShadow = true;
        this.pipeline.add(foundation);

        // Village Kasoon signboard
        const signGeometry = new THREE.PlaneGeometry(6, 2);
        const signMaterial = new THREE.MeshPhongMaterial({ color: 0xFFFFFF });
        const villageSign = new THREE.Mesh(signGeometry, signMaterial);
        villageSign.position.set(150, 8, 10);
        this.pipeline.add(villageSign);
    }

    createCanalTailSystem() {
        // Canal tail water collection points
        for (let i = 0; i < 2; i++) {
            // Canal tail structure
            const canalGeometry = new THREE.BoxGeometry(4, 2, 15);
            const canalMaterial = new THREE.MeshPhongMaterial({ color: 0x4FC3F7 });
            const canal = new THREE.Mesh(canalGeometry, canalMaterial);
            canal.position.set(140 + i * 20, 1, 20 + i * 10);
            canal.castShadow = true;
            this.pipeline.add(canal);

            // 200mm HDPE pipes from canal tails
            const canalPipeGeometry = new THREE.CylinderGeometry(1, 1, 25, 32); // 200mm diameter
            const canalPipeMaterial = new THREE.MeshPhongMaterial({ color: 0x1A1A1A });
            const canalPipe = new THREE.Mesh(canalPipeGeometry, canalPipeMaterial);
            canalPipe.rotation.z = Math.PI / 2;
            canalPipe.position.set(150, -3, 20 + i * 10);
            canalPipe.castShadow = true;
            this.pipeline.add(canalPipe);

            // Pump for canal tail water
            const pumpGeometry = new THREE.BoxGeometry(2, 2, 2);
            const pumpMaterial = new THREE.MeshPhongMaterial({ color: 0xFF5722 });
            const canalPump = new THREE.Mesh(pumpGeometry, pumpMaterial);
            canalPump.position.set(140 + i * 20, 2, 20 + i * 10);
            canalPump.castShadow = true;
            this.pipeline.add(canalPump);
        }
    }

    createSoilLayers() {
        // Topsoil layer
        const topsoilGeometry = new THREE.BoxGeometry(200, 2, 50);
        const topsoilMaterial = new THREE.MeshPhongMaterial({
            color: 0x8D6E63,
            transparent: true,
            opacity: 0.8
        });
        const topsoil = new THREE.Mesh(topsoilGeometry, topsoilMaterial);
        topsoil.position.set(0, -1, 0);
        topsoil.receiveShadow = true;
        this.scene.add(topsoil);

        // Subsoil layer
        const subsoilGeometry = new THREE.BoxGeometry(200, 4, 50);
        const subsoilMaterial = new THREE.MeshPhongMaterial({
            color: 0x6D4C41,
            transparent: true,
            opacity: 0.7
        });
        const subsoil = new THREE.Mesh(subsoilGeometry, subsoilMaterial);
        subsoil.position.set(0, -4, 0);
        subsoil.receiveShadow = true;
        this.scene.add(subsoil);

        // Bedrock layer
        const bedrockGeometry = new THREE.BoxGeometry(200, 3, 50);
        const bedrockMaterial = new THREE.MeshPhongMaterial({
            color: 0x424242,
            transparent: true,
            opacity: 0.6
        });
        const bedrock = new THREE.Mesh(bedrockGeometry, bedrockMaterial);
        bedrock.position.set(0, -7.5, 0);
        bedrock.receiveShadow = true;
        this.scene.add(bedrock);
    }

    createEnvironmentalEffects() {
        // Add trees and vegetation for realism
        this.createTrees();

        // Add buildings and structures
        this.createVillageStructures();

        // Add roads and pathways
        this.createRoads();
    }

    createTrees() {
        // Create trees around the area for environmental realism
        const treePositions = [
            [-70, 0, 20], [-50, 0, 25], [-30, 0, 18],
            [100, 0, 30], [120, 0, -20], [140, 0, 35],
            [180, 0, 40], [200, 0, -30], [220, 0, 25]
        ];

        treePositions.forEach(pos => {
            // Tree trunk
            const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, 8, 8);
            const trunkMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(pos[0], 4, pos[2]);
            trunk.castShadow = true;
            this.scene.add(trunk);

            // Tree foliage
            const foliageGeometry = new THREE.SphereGeometry(4, 8, 6);
            const foliageMaterial = new THREE.MeshPhongMaterial({ color: 0x228B22 });
            const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
            foliage.position.set(pos[0], 10, pos[2]);
            foliage.castShadow = true;
            this.scene.add(foliage);
        });
    }

    createVillageStructures() {
        // Add some village buildings near Kasoon
        const buildingPositions = [
            [160, 0, 40], [170, 0, 45], [180, 0, 50]
        ];

        buildingPositions.forEach((pos, index) => {
            const buildingGeometry = new THREE.BoxGeometry(6, 5, 8);
            const buildingMaterial = new THREE.MeshPhongMaterial({
                color: index % 2 === 0 ? 0xDEB887 : 0xF4A460
            });
            const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
            building.position.set(pos[0], 2.5, pos[2]);
            building.castShadow = true;
            building.receiveShadow = true;
            this.scene.add(building);

            // Roof
            const roofGeometry = new THREE.ConeGeometry(5, 2, 4);
            const roofMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 });
            const roof = new THREE.Mesh(roofGeometry, roofMaterial);
            roof.position.set(pos[0], 6, pos[2]);
            roof.rotation.y = Math.PI / 4;
            roof.castShadow = true;
            this.scene.add(roof);
        });
    }

    createRoads() {
        // Main road from STP to village
        const roadGeometry = new THREE.PlaneGeometry(300, 6);
        const roadMaterial = new THREE.MeshPhongMaterial({ color: 0x2F2F2F });
        const mainRoad = new THREE.Mesh(roadGeometry, roadMaterial);
        mainRoad.rotation.x = -Math.PI / 2;
        mainRoad.position.set(50, -1.8, 15);
        mainRoad.receiveShadow = true;
        this.scene.add(mainRoad);

        // Road markings
        const markingGeometry = new THREE.PlaneGeometry(300, 0.3);
        const markingMaterial = new THREE.MeshPhongMaterial({ color: 0xFFFFFF });
        const roadMarking = new THREE.Mesh(markingGeometry, markingMaterial);
        roadMarking.rotation.x = -Math.PI / 2;
        roadMarking.position.set(50, -1.75, 15);
        this.scene.add(roadMarking);
    }

    createWaterParticleSystem() {
        // Create enhanced particle geometry for realistic water flow
        const particleCount = 500;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        const velocities = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            // Initialize particles along the water path
            positions[i * 3] = -80 + Math.random() * 300; // x - longer path
            positions[i * 3 + 1] = -6 + Math.random() * 8; // y - varied heights
            positions[i * 3 + 2] = -5 + Math.random() * 10; // z - wider spread

            // Realistic water blue colors
            colors[i * 3] = 0.1 + Math.random() * 0.2; // r - less red
            colors[i * 3 + 1] = 0.5 + Math.random() * 0.3; // g - more green
            colors[i * 3 + 2] = 0.8 + Math.random() * 0.2; // b - strong blue

            sizes[i] = 0.2 + Math.random() * 0.4;

            // Add velocity for flow animation
            velocities[i * 3] = 0.5 + Math.random() * 1.0; // x velocity
            velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.2; // y velocity
            velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.1; // z velocity
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        // Enhanced particle material for water effect
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.7,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        this.particleSystem = new THREE.Points(particles, particleMaterial);
        this.scene.add(this.particleSystem);

        // Store references for animation
        this.particlePositions = positions;
        this.particleVelocities = velocities;
        this.particleColors = colors;
    }

    initializeAudio() {
        // Initialize Web Audio API for text-to-speech
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
        }

        // Enhanced Hindi narration script with more detailed timing
        this.narrationScript = [
            {
                time: 3,
                text: "नमस्कार! यह है हरियाणा के उचाना का सीवेज ट्रीटमेंट प्लांट। यहाँ से रोज़ाना साढ़े चार मिलियन लीटर साफ पानी निकलता है।",
                english: "Namaste! This is the Sewage Treatment Plant of Uchana, Haryana. Daily 4.5 million liters of clean water is produced here."
            },
            {
                time: 18,
                text: "इस पंप हाउस की मदद से पानी को तीन सौ मिलीमीटर व्यास की एचडीपीई पाइप में डाला जाता है। यह पाइप भूमिगत है।",
                english: "With the help of this pump house, water is sent into a 300mm diameter HDPE pipe. This pipe is underground."
            },
            {
                time: 33,
                text: "यह पाइपलाइन आठ किलोमीटर की लंबी यात्रा करके गाँव कसून तक पहुँचती है। रास्ते में यह सड़कों के नीचे से गुज़रती है।",
                english: "This pipeline travels a long journey of 8 kilometers to reach Village Kasoon. On the way, it passes under roads."
            },
            {
                time: 43,
                text: "गाँव कसून की पंचायत भूमि पर एक बड़ा जल भंडारण टैंक बनाया गया है। यहाँ सारा पानी इकट्ठा होता है।",
                english: "A large water storage tank has been built on the Panchayat land of Village Kasoon. All water is collected here."
            },
            {
                time: 48,
                text: "नहर की टेल से भी दो सौ मिलीमीटर की पाइप से पानी लाकर इसी टैंक में मिलाया जाता है।",
                english: "Water from canal tails is also brought through 200mm pipes and mixed in this same tank."
            },
            {
                time: 53,
                text: "अब यह पानी तीन चरणों में बांटा जाता है - प्राथमिक, द्वितीयक और तृतीयक नेटवर्क के ज़रिए।",
                english: "Now this water is distributed in three stages - through primary, secondary and tertiary networks."
            },
            {
                time: 58,
                text: "हर हाइड्रेंट चार एकड़ ज़मीन को पानी देता है। इस तरह कुल पच्चीस सौ एकड़ में खेती होती है। धन्यवाद!",
                english: "Each hydrant provides water to 4 acres of land. This way, farming is done on a total of 2500 acres. Thank you!"
            }
        ];

        this.initializeSpeechSynthesis();
    }

    initializeSpeechSynthesis() {
        // Check if speech synthesis is available
        if ('speechSynthesis' in window) {
            this.speechSynthesis = window.speechSynthesis;

            // Wait for voices to load
            const loadVoices = () => {
                this.voices = this.speechSynthesis.getVoices();
                console.log('Available voices:', this.voices.map(v => `${v.name} (${v.lang})`));

                // Priority order for Hindi voices
                const hindiVoicePreferences = [
                    'Microsoft Hemant - Hindi (India)',
                    'Microsoft Kalpana - Hindi (India)',
                    'Google हिन्दी',
                    'Hindi India',
                    'hi-IN'
                ];

                // Try to find the best Hindi voice
                for (let preference of hindiVoicePreferences) {
                    this.hindiVoice = this.voices.find(voice =>
                        voice.name.includes(preference) ||
                        voice.lang.includes('hi-IN') ||
                        voice.lang.includes('hi')
                    );
                    if (this.hindiVoice) break;
                }

                // Fallback to any male voice
                if (!this.hindiVoice) {
                    this.hindiVoice = this.voices.find(voice =>
                        voice.name.toLowerCase().includes('male') ||
                        voice.name.toLowerCase().includes('man') ||
                        voice.name.toLowerCase().includes('hemant')
                    );
                }

                // Final fallback to default voice
                if (!this.hindiVoice && this.voices.length > 0) {
                    this.hindiVoice = this.voices[0];
                }

                if (this.hindiVoice) {
                    console.log('Selected Hindi voice:', this.hindiVoice.name, this.hindiVoice.lang);
                }
            };

            // Load voices immediately if available
            if (this.speechSynthesis.getVoices().length > 0) {
                loadVoices();
            } else {
                // Wait for voices to load
                this.speechSynthesis.onvoiceschanged = loadVoices;

                // Fallback timeout
                setTimeout(loadVoices, 1000);
            }
        }
    }

    speakText(text, isHindi = true) {
        if (!this.speechSynthesis) {
            console.log('Speech synthesis not available');
            return;
        }

        // Stop any current speech
        this.speechSynthesis.cancel();

        // Small delay to ensure cancellation
        setTimeout(() => {
            const utterance = new SpeechSynthesisUtterance(text);

            // Configure voice settings for Hindi
            if (this.hindiVoice) {
                utterance.voice = this.hindiVoice;
                console.log('Using voice:', this.hindiVoice.name);
            }

            // Optimized settings for Hindi male voice
            utterance.rate = 0.75; // Slower for better Hindi pronunciation
            utterance.pitch = 0.8; // Lower pitch for male voice
            utterance.volume = 0.9; // Higher volume

            // Set language explicitly
            if (isHindi) {
                utterance.lang = 'hi-IN';
            } else {
                utterance.lang = 'en-US';
            }

            // Add event listeners
            utterance.onstart = () => {
                console.log('Speech started:', text.substring(0, 50) + '...');
            };

            utterance.onend = () => {
                console.log('Speech ended');
            };

            utterance.onerror = (event) => {
                console.error('Speech error:', event.error);
            };

            this.speechSynthesis.speak(utterance);
            this.currentAudio = utterance;
        }, 100);
    }

    createField() {
        this.field = new THREE.Group();

        // 2500 acres agricultural area (scaled representation)
        const fieldGeometry = new THREE.PlaneGeometry(100, 80); // Larger area to represent 2500 acres
        const fieldMaterial = new THREE.MeshPhongMaterial({
            color: 0x4CAF50,
            shininess: 10
        });
        const fieldBase = new THREE.Mesh(fieldGeometry, fieldMaterial);
        fieldBase.rotation.x = -Math.PI / 2;
        fieldBase.position.set(200, -1.7, 0); // Position after Village Kasoon
        fieldBase.receiveShadow = true;
        this.field.add(fieldBase);

        // Create distribution network
        this.createDistributionNetwork();

        // Crop rows with realistic plants
        this.crops = [];
        for (let row = 0; row < 6; row++) {
            for (let plant = 0; plant < 8; plant++) {
                // Plant stem
                const stemGeometry = new THREE.CylinderGeometry(0.1, 0.15, 2.5, 8);
                const stemMaterial = new THREE.MeshPhongMaterial({ color: 0x689F38 });
                const stem = new THREE.Mesh(stemGeometry, stemMaterial);
                stem.position.set(60 + row * 5, 1.25, -12 + plant * 3);
                stem.castShadow = true;
                this.field.add(stem);

                // Plant leaves
                for (let i = 0; i < 4; i++) {
                    const leafGeometry = new THREE.PlaneGeometry(1.5, 0.8);
                    const leafMaterial = new THREE.MeshPhongMaterial({
                        color: 0x8BC34A,
                        side: THREE.DoubleSide
                    });
                    const leaf = new THREE.Mesh(leafGeometry, leafMaterial);
                    leaf.position.set(
                        60 + row * 5 + (Math.random() - 0.5) * 0.5,
                        1.5 + i * 0.3,
                        -12 + plant * 3 + (Math.random() - 0.5) * 0.5
                    );
                    leaf.rotation.y = (Math.random() - 0.5) * Math.PI;
                    leaf.rotation.z = (Math.random() - 0.5) * 0.3;
                    leaf.castShadow = true;
                    this.field.add(leaf);
                }

                this.crops.push(stem);
            }
        }

        // Irrigation system
        this.createIrrigationSystem();

        this.scene.add(this.field);
    }

    createDistributionNetwork() {
        // Primary distribution network (main lines from storage tank)
        const primaryPipeGeometry = new THREE.CylinderGeometry(0.8, 0.8, 40, 16);
        const primaryPipeMaterial = new THREE.MeshPhongMaterial({ color: 0x2E7D32 });

        // Main primary lines
        for (let i = 0; i < 4; i++) {
            const primaryPipe = new THREE.Mesh(primaryPipeGeometry, primaryPipeMaterial);
            primaryPipe.rotation.x = Math.PI / 2;
            primaryPipe.position.set(170 + i * 20, 0.5, 0);
            primaryPipe.castShadow = true;
            this.field.add(primaryPipe);
        }

        // Secondary distribution network
        const secondaryPipeGeometry = new THREE.CylinderGeometry(0.5, 0.5, 30, 16);
        const secondaryPipeMaterial = new THREE.MeshPhongMaterial({ color: 0x388E3C });

        for (let i = 0; i < 6; i++) {
            for (let j = 0; j < 4; j++) {
                const secondaryPipe = new THREE.Mesh(secondaryPipeGeometry, secondaryPipeMaterial);
                secondaryPipe.rotation.z = Math.PI / 2;
                secondaryPipe.position.set(160 + i * 15, 0.3, -30 + j * 20);
                secondaryPipe.castShadow = true;
                this.field.add(secondaryPipe);
            }
        }

        // Tertiary network with hydrants (4 acres coverage each)
        this.createHydrantNetwork();
    }

    createHydrantNetwork() {
        // Tertiary pipes and hydrants for 4-acre coverage
        const tertiaryPipeGeometry = new THREE.CylinderGeometry(0.3, 0.3, 20, 12);
        const tertiaryPipeMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });

        this.hydrants = [];

        // Create hydrants at intersections where 4 acres meet
        for (let x = 0; x < 8; x++) {
            for (let z = 0; z < 6; z++) {
                // Tertiary pipe
                const tertiaryPipe = new THREE.Mesh(tertiaryPipeGeometry, tertiaryPipeMaterial);
                tertiaryPipe.rotation.x = Math.PI / 2;
                tertiaryPipe.position.set(160 + x * 12, 0.2, -35 + z * 12);
                this.field.add(tertiaryPipe);

                // Hydrant at intersection
                const hydrantGeometry = new THREE.CylinderGeometry(0.4, 0.4, 1.5, 12);
                const hydrantMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
                const hydrant = new THREE.Mesh(hydrantGeometry, hydrantMaterial);
                hydrant.position.set(160 + x * 12, 1, -35 + z * 12);
                hydrant.castShadow = true;
                this.field.add(hydrant);
                this.hydrants.push(hydrant);

                // Hydrant valve
                const valveGeometry = new THREE.SphereGeometry(0.2, 12, 12);
                const valveMaterial = new THREE.MeshPhongMaterial({ color: 0xF44336 });
                const valve = new THREE.Mesh(valveGeometry, valveMaterial);
                valve.position.set(160 + x * 12, 1.8, -35 + z * 12);
                this.field.add(valve);

                // 4-acre coverage indicator
                const coverageGeometry = new THREE.PlaneGeometry(12, 12);
                const coverageMaterial = new THREE.MeshPhongMaterial({
                    color: 0x81C784,
                    transparent: true,
                    opacity: 0.3
                });
                const coverage = new THREE.Mesh(coverageGeometry, coverageMaterial);
                coverage.rotation.x = -Math.PI / 2;
                coverage.position.set(160 + x * 12, -1.5, -35 + z * 12);
                this.field.add(coverage);
            }
        }
    }

    createIrrigationSystem() {
        // Main distribution header
        const headerGeometry = new THREE.CylinderGeometry(0.3, 0.3, 30, 16);
        const headerMaterial = new THREE.MeshPhongMaterial({ color: 0x37474F });
        const mainHeader = new THREE.Mesh(headerGeometry, headerMaterial);
        mainHeader.rotation.x = Math.PI / 2;
        mainHeader.position.set(75, 0.3, 0);
        mainHeader.castShadow = true;
        this.field.add(mainHeader);

        // Drip irrigation laterals
        this.irrigationLines = [];
        for (let i = 0; i < 6; i++) {
            const lineGeometry = new THREE.CylinderGeometry(0.08, 0.08, 24, 12);
            const lineMaterial = new THREE.MeshPhongMaterial({ color: 0x1A1A1A });
            const line = new THREE.Mesh(lineGeometry, lineMaterial);
            line.rotation.x = Math.PI / 2;
            line.position.set(60 + i * 5, 0.15, 0);
            line.castShadow = true;
            this.field.add(line);
            this.irrigationLines.push(line);
        }

        // Drip emitters
        this.drippers = [];
        for (let row = 0; row < 6; row++) {
            for (let emitter = 0; emitter < 8; emitter++) {
                const dripperGeometry = new THREE.SphereGeometry(0.05, 8, 8);
                const dripperMaterial = new THREE.MeshPhongMaterial({ color: 0x2196F3 });
                const dripper = new THREE.Mesh(dripperGeometry, dripperMaterial);
                dripper.position.set(60 + row * 5, 0.1, -12 + emitter * 3);
                this.field.add(dripper);
                this.drippers.push(dripper);
            }
        }

        // Micro-sprinklers for better coverage
        this.sprinklers = [];
        for (let x = 0; x < 3; x++) {
            for (let z = 0; z < 4; z++) {
                // Sprinkler riser
                const riserGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.8, 8);
                const riserMaterial = new THREE.MeshPhongMaterial({ color: 0x424242 });
                const riser = new THREE.Mesh(riserGeometry, riserMaterial);
                riser.position.set(62 + x * 10, 0.4, -9 + z * 6);
                this.field.add(riser);

                // Sprinkler head
                const sprinklerGeometry = new THREE.CylinderGeometry(0.15, 0.1, 0.3, 12);
                const sprinklerMaterial = new THREE.MeshPhongMaterial({ color: 0xFF9800 });
                const sprinkler = new THREE.Mesh(sprinklerGeometry, sprinklerMaterial);
                sprinkler.position.set(62 + x * 10, 0.95, -9 + z * 6);
                sprinkler.castShadow = true;
                this.field.add(sprinkler);
                this.sprinklers.push(sprinkler);

                // Sprinkler nozzle
                const nozzleGeometry = new THREE.SphereGeometry(0.08, 8, 8);
                const nozzleMaterial = new THREE.MeshPhongMaterial({ color: 0x2196F3 });
                const nozzle = new THREE.Mesh(nozzleGeometry, nozzleMaterial);
                nozzle.position.set(62 + x * 10, 1.1, -9 + z * 6);
                this.field.add(nozzle);
            }
        }

        // Control valves for irrigation zones
        for (let i = 0; i < 3; i++) {
            const valveGeometry = new THREE.BoxGeometry(0.5, 0.3, 0.5);
            const valveMaterial = new THREE.MeshPhongMaterial({ color: 0x4CAF50 });
            const valve = new THREE.Mesh(valveGeometry, valveMaterial);
            valve.position.set(58, 0.3, -6 + i * 6);
            valve.castShadow = true;
            this.field.add(valve);
        }

        // Pressure regulator
        const regulatorGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.6, 12);
        const regulatorMaterial = new THREE.MeshPhongMaterial({ color: 0xFF5722 });
        const regulator = new THREE.Mesh(regulatorGeometry, regulatorMaterial);
        regulator.position.set(67, 0.5, 2);
        regulator.castShadow = true;
        this.field.add(regulator);
    }

    setupControls() {
        console.log('Setting up controls...');

        if (this.playBtn) {
            this.playBtn.addEventListener('click', () => {
                console.log('Play button clicked');
                this.startAnimation();
            });
        } else {
            console.error('Play button not found!');
        }

        if (this.pauseBtn) {
            this.pauseBtn.addEventListener('click', () => this.pauseAnimation());
        }

        if (this.resetBtn) {
            this.resetBtn.addEventListener('click', () => this.resetAnimation());
        }

        if (this.audioToggleBtn) {
            this.audioToggleBtn.addEventListener('click', () => this.toggleAudio());
        }

        console.log('Controls setup complete');
    }

    toggleAudio() {
        this.audioEnabled = !this.audioEnabled;

        if (this.audioEnabled) {
            this.audioToggleBtn.textContent = '🔊 Audio ON';
            this.audioToggleBtn.style.background = '#4CAF50';
        } else {
            this.audioToggleBtn.textContent = '🔇 Audio OFF';
            this.audioToggleBtn.style.background = '#F44336';

            // Stop current speech
            if (this.speechSynthesis) {
                this.speechSynthesis.cancel();
            }
        }
    }

    startAnimation() {
        console.log('Starting animation...');
        if (!this.isPlaying) {
            this.isPlaying = true;
            this.startTime = Date.now();
            this.playBtn.disabled = true;
            this.pauseBtn.disabled = false;
            console.log('Animation started successfully');
            this.animate();
        }
    }

    pauseAnimation() {
        this.isPlaying = false;
        this.playBtn.disabled = false;
        this.pauseBtn.disabled = true;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    resetAnimation() {
        this.pauseAnimation();
        this.startTime = 0;
        this.progressFill.style.width = '0%';

        // Reset camera position to start at STP Uchana
        this.camera.position.set(-80, 25, 50);
        this.camera.lookAt(-60, 3, 0);

        // Hide all labels
        Object.values(this.labels).forEach(label => {
            label.classList.remove('show');
        });

        // Reset narration
        if (this.narrationScript) {
            this.narrationScript.forEach(narration => {
                narration.played = false;
            });
        }

        // Stop any current speech
        if (this.speechSynthesis) {
            this.speechSynthesis.cancel();
        }

        // Remove subtitle if present
        const subtitle = document.getElementById('subtitle');
        if (subtitle) {
            subtitle.remove();
        }

        this.renderer.render(this.scene, this.camera);
    }

    animate() {
        if (!this.isPlaying) return;

        const currentTime = Date.now();
        const elapsed = currentTime - this.startTime;
        const progress = Math.min(elapsed / this.duration, 1);

        // Update progress bar
        this.progressFill.style.width = (progress * 100) + '%';

        // Update timeline indicator
        this.updateTimeline(progress);

        // Camera animation (rightward pan)
        this.updateCamera(progress);

        // Label timing
        this.updateLabels(progress);

        // Water flow animation
        this.updateWaterFlow(progress);

        // Audio narration
        this.updateAudioNarration(progress);

        // Render the scene
        this.renderer.render(this.scene, this.camera);

        // Continue animation or stop at end
        if (progress < 1) {
            this.animationId = requestAnimationFrame(() => this.animate());
        } else {
            this.pauseAnimation();
        }
    }

    updateTimeline(progress) {
        const timeInSeconds = progress * 60;

        if (this.timeline) {
            let phaseText = '';

            if (timeInSeconds <= 15) {
                phaseText = '<span class="phase-indicator">Phase 1:</span> STP Uchana Treatment (0-15s)';
            } else if (timeInSeconds <= 30) {
                phaseText = '<span class="phase-indicator">Phase 2:</span> Pump House & Pipeline (15-30s)';
            } else if (timeInSeconds <= 45) {
                phaseText = '<span class="phase-indicator">Phase 3:</span> 8km Underground Journey (30-45s)';
            } else {
                phaseText = '<span class="phase-indicator">Phase 4:</span> Village Kasoon Distribution (45-60s)';
            }

            this.timeline.innerHTML = phaseText;
        }
    }

    updateCamera(progress) {
        const timeInSeconds = progress * 60;

        // Phase-based camera movement over 60 seconds
        let cameraX, cameraY, cameraZ, lookAtX, lookAtY, lookAtZ;

        if (timeInSeconds <= 15) {
            // Phase 1: STP Uchana and outlet (0-15s)
            const phaseProgress = timeInSeconds / 15;
            cameraX = -80 + phaseProgress * 20; // Move from -80 to -60
            cameraY = 25 + Math.sin(phaseProgress * Math.PI) * 5;
            cameraZ = 50;
            lookAtX = -60;
            lookAtY = 3;
            lookAtZ = 0;
        } else if (timeInSeconds <= 30) {
            // Phase 2: Pump house and pipeline start (15-30s)
            const phaseProgress = (timeInSeconds - 15) / 15;
            cameraX = -60 + phaseProgress * 40; // Move from -60 to -20
            cameraY = 20 + Math.sin(phaseProgress * Math.PI * 2) * 3;
            cameraZ = 40;
            lookAtX = -20;
            lookAtY = 0;
            lookAtZ = 0;
        } else if (timeInSeconds <= 45) {
            // Phase 3: 8km pipeline to Village Kasoon (30-45s)
            const phaseProgress = (timeInSeconds - 30) / 15;
            cameraX = -20 + phaseProgress * 170; // Move from -20 to 150 (8km journey)
            cameraY = 30 + Math.sin(phaseProgress * Math.PI) * 8; // Higher for overview
            cameraZ = 60;
            lookAtX = cameraX;
            lookAtY = -3; // Look at underground pipeline
            lookAtZ = 0;
        } else {
            // Phase 4: Village Kasoon storage and distribution network (45-60s)
            const phaseProgress = (timeInSeconds - 45) / 15;
            cameraX = 150 + phaseProgress * 80; // Move from 150 to 230
            cameraY = 35 + Math.sin(phaseProgress * Math.PI * 2) * 10; // Dynamic field overview
            cameraZ = 80 - phaseProgress * 20; // Move closer to see details
            lookAtX = 200; // Focus on 2500 acres area
            lookAtY = 2;
            lookAtZ = 0;
        }

        // Apply smooth easing
        const easedProgress = this.easeInOutCubic(progress);

        this.camera.position.x = cameraX;
        this.camera.position.y = cameraY;
        this.camera.position.z = cameraZ;
        this.camera.lookAt(lookAtX, lookAtY, lookAtZ);
    }

    updateLabels(progress) {
        const timeInSeconds = progress * 60;

        // STP Uchana label (0-15 seconds)
        if (timeInSeconds >= 0 && timeInSeconds <= 18) {
            this.labels.stp.classList.add('show');
        } else {
            this.labels.stp.classList.remove('show');
        }

        // Pump house label (15-30 seconds)
        if (timeInSeconds >= 12 && timeInSeconds <= 33) {
            this.labels.pump.classList.add('show');
        } else {
            this.labels.pump.classList.remove('show');
        }

        // Pipeline label (30-45 seconds)
        if (timeInSeconds >= 28 && timeInSeconds <= 48) {
            this.labels.pipeline.classList.add('show');
        } else {
            this.labels.pipeline.classList.remove('show');
        }

        // Storage tank label (40-50 seconds)
        if (timeInSeconds >= 38 && timeInSeconds <= 52) {
            this.labels.storage.classList.add('show');
        } else {
            this.labels.storage.classList.remove('show');
        }

        // Canal tail label (42-55 seconds)
        if (timeInSeconds >= 40 && timeInSeconds <= 57) {
            this.labels.canal.classList.add('show');
        } else {
            this.labels.canal.classList.remove('show');
        }

        // Distribution network label (45-60 seconds)
        if (timeInSeconds >= 43 && timeInSeconds <= 60) {
            this.labels.network.classList.add('show');
        } else {
            this.labels.network.classList.remove('show');
        }

        // Hydrant label (50-60 seconds)
        if (timeInSeconds >= 48 && timeInSeconds <= 60) {
            this.labels.hydrant.classList.add('show');
        } else {
            this.labels.hydrant.classList.remove('show');
        }

        // Irrigation label (55-60 seconds)
        if (timeInSeconds >= 53 && timeInSeconds <= 60) {
            this.labels.irrigation.classList.add('show');
        } else {
            this.labels.irrigation.classList.remove('show');
        }
    }

    updateWaterFlow(progress) {
        const timeInSeconds = progress * 60;

        // Animate water flow through system based on 60-second timeline
        if (timeInSeconds >= 5) {
            this.animateWaterInSTP();
        }

        if (timeInSeconds >= 15) {
            this.animatePumpOperation();
        }

        if (timeInSeconds >= 30) {
            this.animateWaterInPipeline();
        }

        if (timeInSeconds >= 45) {
            this.animateStorageTank();
        }

        if (timeInSeconds >= 50) {
            this.animateDistributionNetwork();
        }

        if (timeInSeconds >= 55) {
            this.animateIrrigation();
        }
    }

    animateWaterInSTP() {
        // Animate water surface in STP tanks
        if (this.stpWater1 && this.stpWater2) {
            const time = Date.now() * 0.001;
            this.stpWater1.position.y = 2.8 + Math.sin(time * 2) * 0.05;
            this.stpWater2.position.y = 2.8 + Math.sin(time * 2.5) * 0.05;

            // Animate water color to show treatment process
            this.stpWater1.material.color.setHSL(0.55, 0.8, 0.6 + Math.sin(time) * 0.1);
            this.stpWater2.material.color.setHSL(0.55, 0.9, 0.7 + Math.sin(time * 1.2) * 0.1);
        }

        // Animate sump water
        if (this.sumpWater) {
            const time = Date.now() * 0.001;
            this.sumpWater.position.y = 2.5 + Math.sin(time * 3) * 0.03;
        }
    }

    animatePumpOperation() {
        // Rotate pump impeller to show operation
        if (this.pumpImpeller) {
            this.pumpImpeller.rotation.y += 0.3;
        }

        // Animate main pump vibration
        if (this.mainPump) {
            const time = Date.now() * 0.01;
            this.mainPump.position.y = 3 + Math.sin(time * 5) * 0.02;
        }
    }

    animateWaterInPipeline() {
        // Enhanced water particle animation
        if (this.particleSystem && this.particlePositions && this.particleVelocities) {
            const time = Date.now() * 0.001;
            const positions = this.particlePositions;
            const velocities = this.particleVelocities;
            const colors = this.particleColors;

            for (let i = 0; i < positions.length / 3; i++) {
                // Apply velocity-based movement
                positions[i * 3] += velocities[i * 3] * 0.3; // x movement
                positions[i * 3 + 1] += velocities[i * 3 + 1] * 0.1; // y movement
                positions[i * 3 + 2] += velocities[i * 3 + 2] * 0.1; // z movement

                // Add turbulence for realistic water flow
                positions[i * 3 + 1] += Math.sin(time * 3 + i * 0.1) * 0.02;
                positions[i * 3 + 2] += Math.cos(time * 2 + i * 0.15) * 0.015;

                // Reset particles that have moved past the irrigation area
                if (positions[i * 3] > 250) {
                    positions[i * 3] = -80 + Math.random() * 20;
                    positions[i * 3 + 1] = -6 + Math.random() * 4;
                    positions[i * 3 + 2] = -3 + Math.random() * 6;

                    // Reset velocity
                    velocities[i * 3] = 0.5 + Math.random() * 1.0;
                    velocities[i * 3 + 1] = (Math.random() - 0.5) * 0.2;
                    velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.1;
                }

                // Animate color intensity for flow effect
                const flowIntensity = 0.8 + 0.2 * Math.sin(time * 4 + i * 0.2);
                colors[i * 3 + 2] = flowIntensity; // Blue channel
            }

            this.particleSystem.geometry.attributes.position.needsUpdate = true;
            this.particleSystem.geometry.attributes.color.needsUpdate = true;
        }

        // Enhanced pipeline pressure visualization
        if (this.pipeSegments) {
            const time = Date.now() * 0.001;
            this.pipeSegments.forEach((segment, index) => {
                const pressure = 0.05 + 0.03 * Math.sin(time * 3 + index * 0.8);
                segment.material.emissive.setRGB(0, pressure, pressure * 2);

                // Subtle scale animation for pressure effect
                const scale = 1 + 0.01 * Math.sin(time * 5 + index);
                segment.scale.set(scale, scale, 1);
            });
        }
    }

    animateIrrigation() {
        // Animate sprinkler rotation
        if (this.sprinklers) {
            this.sprinklers.forEach((sprinkler, index) => {
                sprinkler.rotation.y += 0.05 + index * 0.01;
            });
        }

        // Animate drip emitters
        if (this.drippers) {
            const time = Date.now() * 0.001;
            this.drippers.forEach((dripper, index) => {
                const scale = 1 + 0.3 * Math.sin(time * 4 + index * 0.2);
                dripper.scale.setScalar(scale);

                // Create water droplet effect
                if (Math.random() < 0.1) {
                    this.createWaterDroplet(dripper.position);
                }
            });
        }

        // Animate crops growing/swaying
        if (this.crops) {
            const time = Date.now() * 0.001;
            this.crops.forEach((crop, index) => {
                crop.rotation.z = Math.sin(time + index * 0.1) * 0.05;
                crop.scale.y = 1 + 0.02 * Math.sin(time * 0.5 + index * 0.05);
            });
        }
    }

    createWaterDroplet(position) {
        // Create temporary water droplet
        const dropletGeometry = new THREE.SphereGeometry(0.02, 8, 8);
        const dropletMaterial = new THREE.MeshPhongMaterial({
            color: 0x4DD0E1,
            transparent: true,
            opacity: 0.8
        });
        const droplet = new THREE.Mesh(dropletGeometry, dropletMaterial);
        droplet.position.copy(position);
        droplet.position.y -= 0.1;

        this.scene.add(droplet);

        // Animate droplet falling
        const fallAnimation = () => {
            droplet.position.y -= 0.05;
            droplet.material.opacity -= 0.02;

            if (droplet.position.y > -2 && droplet.material.opacity > 0) {
                requestAnimationFrame(fallAnimation);
            } else {
                this.scene.remove(droplet);
            }
        };

        fallAnimation();
    }

    animateStorageTank() {
        // Animate water level in Village Kasoon storage tank
        // This would show water filling and distribution
    }

    animateDistributionNetwork() {
        // Animate water flow through primary, secondary, and tertiary networks
        if (this.hydrants) {
            const time = Date.now() * 0.001;
            this.hydrants.forEach((hydrant, index) => {
                // Animate hydrant operation
                hydrant.scale.y = 1 + 0.1 * Math.sin(time * 2 + index * 0.3);
            });
        }
    }

    updateAudioNarration(progress) {
        if (!this.audioEnabled) return;

        const timeInSeconds = progress * 60;

        // Check if any narration should be triggered
        this.narrationScript.forEach((narration, index) => {
            // Trigger narration at the specified time (with 0.5 second tolerance)
            if (Math.abs(timeInSeconds - narration.time) < 0.5 && !narration.played) {
                this.speakText(narration.text, true);
                narration.played = true;

                // Show subtitle
                this.showSubtitle(narration.text, narration.english);
            }
        });
    }

    showSubtitle(hindiText, englishText) {
        // Create or update subtitle display
        let subtitleDiv = document.getElementById('subtitle');
        if (!subtitleDiv) {
            subtitleDiv = document.createElement('div');
            subtitleDiv.id = 'subtitle';
            subtitleDiv.style.cssText = `
                position: absolute;
                bottom: 200px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                font-size: 16px;
                text-align: center;
                max-width: 80%;
                z-index: 200;
                border: 2px solid #4CAF50;
            `;
            document.body.appendChild(subtitleDiv);
        }

        subtitleDiv.innerHTML = `
            <div style="color: #4CAF50; font-weight: bold; margin-bottom: 5px;">${hindiText}</div>
            <div style="color: #FFD700; font-size: 14px;">${englishText}</div>
        `;

        // Auto-hide subtitle after 4 seconds
        setTimeout(() => {
            if (subtitleDiv) {
                subtitleDiv.style.opacity = '0';
                setTimeout(() => {
                    if (subtitleDiv && subtitleDiv.parentNode) {
                        subtitleDiv.parentNode.removeChild(subtitleDiv);
                    }
                }, 500);
            }
        }, 4000);
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// Initialize the animation when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new WaterInfrastructureAnimation();
});
