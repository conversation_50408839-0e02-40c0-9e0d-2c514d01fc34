<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Infrastructure 3D Animation</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 50%, #8B4513 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #canvas-container {
            width: 100%;
            height: 100%;
        }
        
        .label {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9));
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border: 3px solid #007acc;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.6);
            opacity: 0;
            transition: all 0.6s ease-in-out;
            z-index: 100;
            pointer-events: none;
            backdrop-filter: blur(5px);
            transform: translateY(-10px);
        }

        .label.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .label.hindi {
            font-size: 20px;
            color: #d32f2f;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .label.english {
            font-size: 16px;
            color: #1976d2;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        }

        #stp-label {
            top: 15%;
            left: 8%;
            max-width: 250px;
        }

        #pump-label {
            top: 20%;
            left: 32%;
            max-width: 200px;
        }

        #pipeline-label {
            top: 65%;
            left: 45%;
            max-width: 300px;
        }

        #storage-label {
            top: 25%;
            left: 65%;
            max-width: 250px;
        }

        #canal-label {
            top: 45%;
            left: 70%;
            max-width: 250px;
        }

        #network-label {
            top: 35%;
            left: 75%;
            max-width: 280px;
        }

        #hydrant-label {
            top: 55%;
            left: 80%;
            max-width: 200px;
        }

        #irrigation-label {
            top: 75%;
            left: 85%;
            max-width: 220px;
        }

        .tech-specs {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-width: 300px;
            z-index: 150;
        }

        .tech-specs h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
            font-size: 14px;
        }

        .tech-specs .spec-item {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        .spec-value {
            color: #FFD700;
            font-weight: bold;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 200;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        button:hover {
            background: #005a9e;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        #progress-bar {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
        }
        
        #progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #2196F3 50%, #FF9800 100%);
            width: 0%;
            transition: width 0.1s ease;
        }

        .timeline {
            position: absolute;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 150;
        }

        .phase-indicator {
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <!-- Labels for different sections -->
        <div id="stp-label" class="label hindi">STP Uchana, Haryana<br><span class="english">4.5 MLD Discharge</span></div>
        <div id="pump-label" class="label hindi">पंप हाउस<br><span class="english">300mm HDPE Pipeline</span></div>
        <div id="pipeline-label" class="label hindi">भूमिगत पाइपलाइन<br><span class="english">8 Km to Village Kasoon</span></div>
        <div id="storage-label" class="label hindi">जल भंडारण टैंक<br><span class="english">Panchayat Land Storage</span></div>
        <div id="canal-label" class="label hindi">नहर टेल<br><span class="english">Canal Tail Water (200mm HDPE)</span></div>
        <div id="network-label" class="label hindi">वितरण नेटवर्क<br><span class="english">Primary → Secondary → Tertiary</span></div>
        <div id="hydrant-label" class="label hindi">हाइड्रेंट<br><span class="english">4 Acres Coverage</span></div>
        <div id="irrigation-label" class="label hindi">सूक्ष्म सिंचाई<br><span class="english">2500 Acres Total</span></div>

        <!-- Technical Specifications Panel -->
        <div class="tech-specs">
            <h3>🔧 Project Specifications</h3>
            <div style="color: #FFD700; font-size: 11px; margin-bottom: 10px;">
                🎵 Hindi Audio Narration Available
            </div>
            <div class="spec-item">
                <span>Source:</span>
                <span class="spec-value">STP Uchana, Haryana</span>
            </div>
            <div class="spec-item">
                <span>Destination:</span>
                <span class="spec-value">Village Kasoon</span>
            </div>
            <div class="spec-item">
                <span>Discharge:</span>
                <span class="spec-value">4.5 MLD</span>
            </div>
            <div class="spec-item">
                <span>Distance:</span>
                <span class="spec-value">8 Km</span>
            </div>
            <div class="spec-item">
                <span>Main Pipeline:</span>
                <span class="spec-value">300mm HDPE</span>
            </div>
            <div class="spec-item">
                <span>Canal Pipes:</span>
                <span class="spec-value">200mm HDPE</span>
            </div>
            <div class="spec-item">
                <span>Irrigation Area:</span>
                <span class="spec-value">2500 Acres</span>
            </div>
            <div class="spec-item">
                <span>Hydrant Coverage:</span>
                <span class="spec-value">4 Acres each</span>
            </div>
        </div>

        <!-- Timeline indicator -->
        <div id="timeline" class="timeline">
            <span class="phase-indicator">Phase 1:</span> STP Uchana Treatment (0-15s)
        </div>

        <!-- Progress bar -->
        <div id="progress-bar">
            <div id="progress-fill"></div>
        </div>
        
        <!-- Controls -->
        <div id="controls">
            <button id="play-btn">🎬 Play Animation</button>
            <button id="pause-btn" disabled>⏸️ Pause</button>
            <button id="reset-btn">🔄 Reset</button>
            <button id="audio-toggle" title="Toggle Audio Narration">🔊 Audio ON</button>
        </div>
    </div>
    
    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="animation.js"></script>
</body>
</html>
