<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Infrastructure 3D Animation</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 50%, #8B4513 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #canvas-container {
            width: 100%;
            height: 100%;
        }
        
        .label {
            position: absolute;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            border: 2px solid #007acc;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            z-index: 100;
            pointer-events: none;
        }
        
        .label.hindi {
            font-size: 18px;
            color: #d32f2f;
        }
        
        .label.english {
            font-size: 14px;
            color: #1976d2;
        }
        
        #stp-label {
            top: 20%;
            left: 10%;
        }
        
        #pump-label {
            top: 25%;
            left: 35%;
        }
        
        #pipeline-label {
            top: 70%;
            left: 50%;
        }
        
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 200;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        button:hover {
            background: #005a9e;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        #progress-bar {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
        }
        
        #progress-fill {
            height: 100%;
            background: #007acc;
            width: 0%;
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <!-- Labels for different sections -->
        <div id="stp-label" class="label hindi">STP (उपचारित जल)</div>
        <div id="pump-label" class="label hindi">पंप हाउस</div>
        <div id="pipeline-label" class="label hindi">भूमिगत पाइपलाइन (Underground Pipeline)</div>
        
        <!-- Progress bar -->
        <div id="progress-bar">
            <div id="progress-fill"></div>
        </div>
        
        <!-- Controls -->
        <div id="controls">
            <button id="play-btn">Play Animation</button>
            <button id="pause-btn" disabled>Pause</button>
            <button id="reset-btn">Reset</button>
        </div>
    </div>
    
    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="animation.js"></script>
</body>
</html>
